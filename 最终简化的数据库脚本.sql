-- SeatMaster自动预约功能数据库更新脚本
-- 极简版本：最小化数据库变更

USE seatmaster;

-- 1. 添加必要字段到reservations表 (只添加2个字段)
ALTER TABLE reservations ADD COLUMN last_execution_time TIMESTAMP NULL COMMENT '最后执行时间';
ALTER TABLE reservations ADD COLUMN execution_result TEXT COMMENT '执行结果(JSON)';

-- 2. 扩展status枚举 (只添加必要的自动预约状态)
ALTER TABLE reservations MODIFY status ENUM('ACTIVE', 'CANCELLED', 'AUTO_PENDING', 'AUTO_SUCCESS', 'AUTO_FAILED') NOT NULL DEFAULT 'ACTIVE';

-- 3. 创建执行日志表
CREATE TABLE auto_execution_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    reservation_id BIGINT NOT NULL COMMENT '预约记录ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    execution_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '执行时间',
    execution_status ENUM('SUCCESS', 'FAILED', 'TIMEOUT') NOT NULL,
    result_message TEXT COMMENT '执行结果信息',
    execution_duration INT COMMENT '执行耗时(秒)',
    
    INDEX idx_reservation_time (reservation_id, execution_time),
    INDEX idx_user_status (user_id, execution_status),
    FOREIGN KEY (reservation_id) REFERENCES reservations(id),
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- 4. 验证更改
DESCRIBE reservations;
DESCRIBE auto_execution_logs;

-- 5. 测试数据插入 (可选)
-- INSERT INTO reservations (user_id, room_id, seat_id, start_time, end_time, reservation_open_time, reservation_type, status) 
-- VALUES (1, 1769, '001', '08:00', '18:00', '08:00:00', 'ADVANCE_ONE_DAY', 'AUTO_PENDING');

COMMIT;

-- 完成！
-- 总结：
-- - 只添加了2个字段到reservations表
-- - 只添加了3个新的status值
-- - 创建了1个日志表
-- - 完全利用现有的users表数据
-- - 数据库变更最小化，风险最低
