package com.seatmaster.service;

import com.seatmaster.dto.ExecutionResult;
import com.seatmaster.entity.Reservation;
import com.seatmaster.entity.User;
import com.seatmaster.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 学习通API服务
 * 负责与学习通系统进行交互，执行自动预约
 */
@Service
@Slf4j
public class XuexitongApiService {
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private RestTemplate restTemplate;
    
    @Value("${xuexitong.base-url:https://passport2.chaoxing.com}")
    private String baseUrl;
    
    @Value("${xuexitong.office-url:https://office.chaoxing.com}")
    private String officeUrl;
    
    @Value("${xuexitong.timeout:30000}")
    private int timeout;
    
    // API路径常量
    private static final String LOGIN_URL = "/fanyalogin";
    private static final String SUBMIT_URL = "/data/apps/seat/submit";
    private static final String SEAT_URL = "/data/apps/seat/getusedtimes";
    
    // AES加密相关常量 (从Python脚本中提取)
    private static final String AES_KEY = "u2oh6Vu^HWe4_AES";
    private static final String AES_IV = "u2oh6Vu^HWe4_AES";
    
    /**
     * 执行学习通预约 - 主入口方法
     * 
     * @param reservation 预约信息
     * @return 执行结果
     */
    public ExecutionResult executeReservation(Reservation reservation) {
        long startTime = System.currentTimeMillis();
        
        try {
            log.info("开始执行学习通预约: reservationId={}, userId={}, roomId={}, seatId={}", 
                reservation.getId(), reservation.getUserId(), reservation.getRoomId(), reservation.getSeatId());
            
            // 1. 获取用户信息
            User user = userService.getById(reservation.getUserId());
            if (user == null) {
                return ExecutionResult.failure("用户不存在", "USER_NOT_FOUND");
            }
            
            // 2. 检查用户剩余天数
            if (user.getRemainingDays() <= 0) {
                return ExecutionResult.failure("用户剩余天数不足", "INSUFFICIENT_DAYS");
            }
            
            // 3. 创建学习通会话
            XuexitongSession session = new XuexitongSession();
            
            // 4. 登录学习通
            if (!session.login(user.getUsername(), user.getPassword())) {
                return ExecutionResult.failure("学习通登录失败", "LOGIN_FAILED");
            }
            
            // 5. 执行预约
            boolean success = session.submitReservation(
                reservation.getRoomId(),
                reservation.getSeatId(),
                reservation.getStartTime().toString(),
                reservation.getEndTime().toString()
            );
            
            long duration = System.currentTimeMillis() - startTime;
            
            if (success) {
                log.info("学习通预约成功: userId={}, roomId={}, seatId={}, duration={}ms", 
                    user.getId(), reservation.getRoomId(), reservation.getSeatId(), duration);
                
                return ExecutionResult.success(
                    "预约成功", 
                    String.format("成功预约房间%d座位%s，耗时%dms", 
                        reservation.getRoomId(), reservation.getSeatId(), duration)
                ).withDuration(duration);
            } else {
                return ExecutionResult.failure("座位预约失败", "RESERVATION_FAILED")
                    .withDuration(duration);
            }
            
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("学习通预约异常: userId={}, error={}, duration={}ms", 
                reservation.getUserId(), e.getMessage(), duration, e);
            
            return ExecutionResult.failure("预约异常: " + e.getMessage(), "SYSTEM_ERROR")
                .withDuration(duration);
        }
    }
    
    /**
     * 学习通会话管理内部类
     * 负责维护与学习通的HTTP会话
     */
    private class XuexitongSession {
        private final Map<String, String> cookies = new HashMap<>();
        private String token;
        
        /**
         * 登录学习通
         * 
         * @param username 用户名
         * @param password 密码
         * @return 登录是否成功
         */
        public boolean login(String username, String password) {
            try {
                log.debug("开始登录学习通: username={}", username);
                
                // 1. 获取登录页面，建立会话
                getLoginPage();
                
                // 2. 构建登录参数
                MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
                params.add("fid", "-1");
                params.add("uname", aesEncrypt(username));
                params.add("password", aesEncrypt(password));
                params.add("refer", "http%3A%2F%2Foffice.chaoxing.com%2Ffront%2Fthird%2Fapps%2Fseat%2Fcode");
                params.add("t", "true");
                
                // 3. 发送登录请求
                HttpHeaders headers = createHeaders();
                headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
                
                HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(params, headers);
                
                ResponseEntity<String> response = restTemplate.postForEntity(
                    baseUrl + LOGIN_URL, request, String.class);
                
                // 4. 解析登录结果
                if (response.getStatusCode().is2xxSuccessful()) {
                    String responseBody = response.getBody();
                    Map<String, Object> result = JsonUtils.fromJsonToMap(responseBody);
                    
                    if (result != null && Boolean.TRUE.equals(result.get("status"))) {
                        // 提取Cookie
                        extractCookies(response);
                        log.info("学习通登录成功: username={}", username);
                        return true;
                    } else {
                        String errorMsg = result != null ? (String) result.get("msg2") : "未知错误";
                        log.warn("学习通登录失败: username={}, message={}", username, errorMsg);
                    }
                }
                
                return false;
                
            } catch (Exception e) {
                log.error("学习通登录异常: username={}, error={}", username, e.getMessage(), e);
                return false;
            }
        }
        
        /**
         * 提交预约
         * 
         * @param roomId 房间ID
         * @param seatId 座位ID
         * @param startTime 开始时间
         * @param endTime 结束时间
         * @return 预约是否成功
         */
        public boolean submitReservation(Long roomId, String seatId, String startTime, String endTime) {
            try {
                log.debug("开始提交预约: roomId={}, seatId={}, startTime={}, endTime={}", 
                    roomId, seatId, startTime, endTime);
                
                // 1. 获取页面token
                String pageToken = getPageToken(roomId, seatId);
                if (pageToken == null) {
                    log.error("获取页面token失败");
                    return false;
                }
                
                // 2. 构建预约参数
                Map<String, String> submitParams = new HashMap<>();
                submitParams.put("roomId", roomId.toString());
                submitParams.put("seatId", seatId);
                submitParams.put("startTime", startTime);
                submitParams.put("endTime", endTime);
                submitParams.put("token", pageToken);
                submitParams.put("captcha", ""); // 暂不处理验证码
                
                // 3. 计算enc参数 (参数签名)
                String enc = calculateEnc(submitParams);
                submitParams.put("enc", enc);
                
                // 4. 发送预约请求
                HttpHeaders headers = createHeaders();
                headers.add("Host", "office.chaoxing.com");
                headers.add("Referer", officeUrl + "/front/third/apps/seat/code?id=" + roomId + "&seatNum=" + seatId);
                
                MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
                submitParams.forEach(params::add);
                
                HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(params, headers);
                
                ResponseEntity<String> response = restTemplate.postForEntity(
                    officeUrl + SUBMIT_URL, request, String.class);
                
                // 5. 解析预约结果
                if (response.getStatusCode().is2xxSuccessful()) {
                    String responseBody = response.getBody();
                    Map<String, Object> result = JsonUtils.fromJsonToMap(responseBody);
                    
                    if (result != null) {
                        boolean success = Boolean.TRUE.equals(result.get("success"));
                        String message = (String) result.get("msg");
                        
                        log.info("预约提交结果: roomId={}, seatId={}, success={}, message={}", 
                            roomId, seatId, success, message);
                        
                        return success;
                    }
                }
                
                return false;
                
            } catch (Exception e) {
                log.error("提交预约异常: roomId={}, seatId={}, error={}", roomId, seatId, e.getMessage(), e);
                return false;
            }
        }
        
        /**
         * 获取登录页面，建立会话
         */
        private void getLoginPage() {
            try {
                HttpHeaders headers = createHeaders();
                HttpEntity<String> request = new HttpEntity<>(headers);
                
                ResponseEntity<String> response = restTemplate.exchange(
                    baseUrl + "/mlogin?loginType=1&newversion=true&fid=", 
                    HttpMethod.GET, request, String.class);
                
                extractCookies(response);
                log.debug("获取登录页面成功");
            } catch (Exception e) {
                log.warn("获取登录页面失败: {}", e.getMessage());
            }
        }
        
        /**
         * 获取页面token
         * 
         * @param roomId 房间ID
         * @param seatId 座位ID
         * @return token字符串
         */
        private String getPageToken(Long roomId, String seatId) {
            try {
                String url = officeUrl + "/front/third/apps/seat/code?id=" + roomId + "&seatNum=" + seatId;
                
                HttpHeaders headers = createHeaders();
                HttpEntity<String> request = new HttpEntity<>(headers);
                
                ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, request, String.class);
                
                if (response.getStatusCode().is2xxSuccessful()) {
                    String html = response.getBody();
                    
                    // 提取token
                    Pattern pattern = Pattern.compile("token\\s*[=:]\\s*['\"]([^'\"]+)['\"]");
                    Matcher matcher = pattern.matcher(html);
                    
                    if (matcher.find()) {
                        String token = matcher.group(1);
                        log.debug("获取页面token成功: {}", token);
                        return token;
                    }
                }
                
                log.error("从页面HTML中未找到token");
                return null;
                
            } catch (Exception e) {
                log.error("获取页面token异常: roomId={}, seatId={}, error={}", roomId, seatId, e.getMessage(), e);
                return null;
            }
        }

        /**
         * 创建HTTP请求头
         *
         * @return HttpHeaders
         */
        private HttpHeaders createHeaders() {
            HttpHeaders headers = new HttpHeaders();
            headers.add("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36");
            headers.add("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8");
            headers.add("Accept-Language", "zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2");
            headers.add("Accept-Encoding", "gzip, deflate");
            headers.add("Connection", "keep-alive");

            // 添加Cookie
            if (!cookies.isEmpty()) {
                String cookieString = cookies.entrySet().stream()
                    .map(entry -> entry.getKey() + "=" + entry.getValue())
                    .reduce((a, b) -> a + "; " + b)
                    .orElse("");
                headers.add("Cookie", cookieString);
            }

            return headers;
        }

        /**
         * 从响应中提取Cookie
         *
         * @param response HTTP响应
         */
        private void extractCookies(ResponseEntity<String> response) {
            List<String> setCookieHeaders = response.getHeaders().get("Set-Cookie");
            if (setCookieHeaders != null) {
                for (String cookie : setCookieHeaders) {
                    String[] parts = cookie.split(";")[0].split("=", 2);
                    if (parts.length == 2) {
                        cookies.put(parts[0], parts[1]);
                    }
                }
                log.debug("提取到 {} 个Cookie", cookies.size());
            }
        }
    }

    /**
     * AES加密 (复制Python脚本的加密逻辑)
     *
     * @param plainText 明文
     * @return 加密后的Base64字符串
     */
    private String aesEncrypt(String plainText) {
        try {
            SecretKeySpec keySpec = new SecretKeySpec(AES_KEY.getBytes(StandardCharsets.UTF_8), "AES");
            IvParameterSpec ivSpec = new IvParameterSpec(AES_IV.getBytes(StandardCharsets.UTF_8));

            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            cipher.init(Cipher.ENCRYPT_MODE, keySpec, ivSpec);

            byte[] encrypted = cipher.doFinal(plainText.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(encrypted);

        } catch (Exception e) {
            log.error("AES加密失败: {}", e.getMessage(), e);
            return plainText;
        }
    }

    /**
     * 计算enc参数 (参数签名)
     *
     * @param params 参数Map
     * @return 签名字符串
     */
    private String calculateEnc(Map<String, String> params) {
        try {
            // 按照Python脚本的逻辑计算参数签名
            StringBuilder sb = new StringBuilder();
            params.entrySet().stream()
                .sorted(Map.Entry.comparingByKey())
                .forEach(entry -> sb.append(entry.getKey()).append("=").append(entry.getValue()).append("&"));

            String paramString = sb.toString();
            if (paramString.endsWith("&")) {
                paramString = paramString.substring(0, paramString.length() - 1);
            }

            // MD5加密
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] digest = md.digest(paramString.getBytes(StandardCharsets.UTF_8));

            StringBuilder hexString = new StringBuilder();
            for (byte b : digest) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }

            return hexString.toString();

        } catch (Exception e) {
            log.error("计算enc参数失败: {}", e.getMessage(), e);
            return "";
        }
    }
}
