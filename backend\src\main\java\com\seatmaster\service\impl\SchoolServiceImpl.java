package com.seatmaster.service.impl;

import com.seatmaster.entity.School;
import com.seatmaster.mapper.SchoolMapper;
import com.seatmaster.service.SchoolService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class SchoolServiceImpl implements SchoolService {
    
    private final SchoolMapper schoolMapper;
    
    @Override
    public List<School> getAllSchools() {
        return schoolMapper.selectList(null);
    }

    @Override
    public School getSchoolById(Long id) {
        return schoolMapper.selectById(id);
    }

    @Override
    public School createSchool(School school) {
        school.setCreatedTime(java.time.LocalDateTime.now());
        schoolMapper.insert(school);
        return school;
    }

    @Override
    public School updateSchool(School school) {
        schoolMapper.updateById(school);
        return school;
    }

    @Override
    public boolean deleteSchool(Long id) {
        return schoolMapper.deleteById(id) > 0;
    }
}