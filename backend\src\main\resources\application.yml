server:
  port: 8080

spring:
  datasource:
    url: *****************************************************************************************************************************
    username: root
    password: root
    driver-class-name: com.mysql.cj.jdbc.Driver
  task:
    scheduling:
      enabled: true
      pool:
        size: 10

mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

jwt:
  secret: seatMasterSecretKeyForHS512AlgorithmMustBeAtLeast512BitsLongToMeetJWTSpecificationRequirements2023
  expiration: ********* # 7 days (7 * 24 * 60 * 60 * 1000)

# 学习通API配置
xuexitong:
  base-url: "https://passport2.chaoxing.com"
  office-url: "https://office.chaoxing.com"
  timeout: 30000
  retry-count: 3

# 自动预约配置
auto-reservation:
  enabled: true
  scheduler:
    enabled: true
    max-concurrent: 5
    cron: "0 * * * * ?"  # 每分钟执行一次

# 定时任务配置已合并到上面的spring配置中

logging:
  level:
    com.seatmaster: debug
    com.seatmaster.service.XuexitongApiService: DEBUG
    com.seatmaster.service.AutoReservationSchedulerService: DEBUG