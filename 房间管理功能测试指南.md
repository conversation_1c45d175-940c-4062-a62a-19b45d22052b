# 房间管理功能测试指南

## 🎯 功能概述

新增的房间管理功能允许管理员：
1. **学校管理**：添加、编辑、删除学校
2. **房间管理**：添加、编辑、删除房间，并可按学校筛选

## 🚀 如何访问

### 方法1：通过Dashboard卡片
1. 以管理员身份登录系统
2. 在Dashboard页面点击"房间管理"卡片

### 方法2：通过Header按钮
1. 以管理员身份登录系统
2. 点击页面顶部的"房间管理"按钮

### 方法3：直接访问URL
```
http://localhost:5173/room-management
```

## 📋 功能测试步骤

### 学校管理测试

#### 1. 添加学校
- 点击"学校管理"标签页
- 点击"添加学校"按钮
- 输入学校名称（例如：清华大学）
- 点击"创建"按钮
- 验证学校是否出现在列表中

#### 2. 编辑学校
- 在学校列表中点击某个学校的"编辑"按钮
- 修改学校名称
- 点击"保存"按钮
- 验证修改是否生效

#### 3. 删除学校
- 在学校列表中点击某个学校的"删除"按钮
- 确认删除操作
- 验证学校是否从列表中消失

### 房间管理测试

#### 1. 添加房间
- 点击"房间管理"标签页
- 点击"添加房间"按钮
- 填写房间信息：
  - 所属学校：选择已创建的学校
  - 房间名称：例如"图书馆一楼"
  - 房间编号：例如"LIB-001"
  - 总座位数：例如100
  - 最大预约时长：例如8小时
  - 描述：例如"安静的学习环境"
- 点击"创建"按钮
- 验证房间是否出现在列表中

#### 2. 编辑房间
- 在房间列表中点击某个房间的"编辑"按钮
- 修改房间信息
- 点击"保存"按钮
- 验证修改是否生效

#### 3. 删除房间
- 在房间列表中点击某个房间的"删除"按钮
- 确认删除操作
- 验证房间是否从列表中消失

#### 4. 按学校筛选房间
- 在房间管理页面使用学校筛选下拉框
- 选择特定学校
- 验证只显示该学校的房间
- 清除筛选条件，验证显示所有房间

## 🔧 后端API测试

### 学校管理API

```bash
# 获取所有学校
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     http://localhost:8080/api/admin/room-management/schools

# 创建学校
curl -X POST \
     -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     -H "Content-Type: application/json" \
     -d '{"name":"测试大学"}' \
     http://localhost:8080/api/admin/room-management/schools

# 更新学校
curl -X PUT \
     -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     -H "Content-Type: application/json" \
     -d '{"name":"更新后的大学名称"}' \
     http://localhost:8080/api/admin/room-management/schools/1

# 删除学校
curl -X DELETE \
     -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     http://localhost:8080/api/admin/room-management/schools/1
```

### 房间管理API

```bash
# 获取所有房间
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     http://localhost:8080/api/admin/room-management/rooms

# 获取指定学校的房间
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     http://localhost:8080/api/admin/room-management/schools/1/rooms

# 创建房间
curl -X POST \
     -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     -H "Content-Type: application/json" \
     -d '{
       "schoolId": 1,
       "name": "图书馆一楼",
       "roomId": "LIB-001",
       "totalSeats": 100,
       "maxReservationHours": 8,
       "description": "安静的学习环境"
     }' \
     http://localhost:8080/api/admin/room-management/rooms

# 更新房间
curl -X PUT \
     -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     -H "Content-Type: application/json" \
     -d '{
       "schoolId": 1,
       "name": "更新后的房间名称",
       "roomId": "LIB-001-UPDATED",
       "totalSeats": 120,
       "maxReservationHours": 10,
       "description": "更新后的描述"
     }' \
     http://localhost:8080/api/admin/room-management/rooms/1

# 删除房间
curl -X DELETE \
     -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     http://localhost:8080/api/admin/room-management/rooms/1
```

## 🔍 验证要点

### 数据库验证
```sql
-- 查看学校表
SELECT * FROM schools ORDER BY created_time DESC;

-- 查看房间表
SELECT r.*, s.name as school_name 
FROM rooms r 
LEFT JOIN schools s ON r.school_id = s.id 
ORDER BY r.created_time DESC;
```

### 前端验证
1. **响应式设计**：在不同屏幕尺寸下测试界面
2. **数据刷新**：操作后数据是否及时更新
3. **错误处理**：输入无效数据时的错误提示
4. **权限控制**：非管理员用户无法访问

### 后端验证
1. **权限验证**：只有管理员可以访问API
2. **数据验证**：输入数据的格式和范围验证
3. **关联删除**：删除学校时相关房间的处理
4. **事务处理**：操作失败时的回滚机制

## 🚨 注意事项

1. **权限要求**：只有管理员用户可以访问房间管理功能
2. **数据关联**：删除学校会影响相关的房间数据
3. **预约影响**：删除房间会影响相关的预约记录
4. **数据验证**：确保输入的数据符合业务规则

## 🎉 预期结果

完成测试后，您应该能够：
- ✅ 成功创建、编辑、删除学校
- ✅ 成功创建、编辑、删除房间
- ✅ 按学校筛选房间列表
- ✅ 看到实时更新的数据
- ✅ 获得良好的用户体验

如果遇到任何问题，请检查：
1. 后端服务是否正常运行
2. 数据库连接是否正常
3. 用户是否具有管理员权限
4. 网络请求是否成功
