package com.seatmaster.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.seatmaster.entity.Room;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface RoomMapper extends BaseMapper<Room> {
    
    /**
     * 根据学校ID查询房间列表
     * @param schoolId 学校ID
     * @return 房间列表
     */
    @Select("SELECT * FROM rooms WHERE school_id = #{schoolId} ORDER BY name")
    List<Room> findBySchoolId(Long schoolId);
    
    /**
     * 查询所有房间
     * @return 房间列表
     */
    @Select("SELECT r.* FROM rooms r ORDER BY r.school_id, r.name")
    List<Room> findAllWithAvailableSeats();
} 