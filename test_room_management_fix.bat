@echo off
echo === 房间管理功能修复验证 ===

set BASE_URL=http://localhost:8080/api

echo 1. 测试管理员登录（使用正确密码 admin123）...
curl -s -X POST -H "Content-Type: application/json" -d "{\"username\":\"admin\",\"password\":\"admin123\"}" %BASE_URL%/auth/login > login_response.txt

echo 登录响应：
type login_response.txt

echo.
echo 2. 提取token并测试房间管理API...
echo 请手动复制上面的token，然后运行以下命令：
echo.
echo 测试学校列表：
echo curl -H "Authorization: Bearer YOUR_TOKEN" %BASE_URL%/admin/room-management/schools
echo.
echo 测试房间列表：
echo curl -H "Authorization: Bearer YOUR_TOKEN" %BASE_URL%/admin/room-management/rooms
echo.
echo === 修复说明 ===
echo 1. 已将数据库中管理员密码更新为 admin123（与文档一致）
echo 2. 已修复前端代码中的数组安全检查
echo 3. 现在可以使用 admin/admin123 正常登录并访问房间管理功能
echo.
echo === 使用方法 ===
echo 1. 在前端登录页面使用：用户名 admin，密码 admin123
echo 2. 登录后点击"房间管理"即可正常使用
echo.
pause
