@echo off
echo === 房间管理API测试 ===

set BASE_URL=http://localhost:8080/api

echo 1. 测试管理员登录...
curl -s -X POST -H "Content-Type: application/json" -d "{\"username\":\"admin\",\"password\":\"admin123\"}" %BASE_URL%/auth/login > login_response.txt
type login_response.txt

echo.
echo 2. 测试控制器连通性...
echo 请手动复制上面的token，然后运行以下命令：
echo curl -H "Authorization: Bearer YOUR_TOKEN" %BASE_URL%/admin/room-management/test

echo.
echo 3. 测试获取学校列表...
echo curl -H "Authorization: Bearer YOUR_TOKEN" %BASE_URL%/admin/room-management/schools

echo.
echo 4. 测试获取房间列表...
echo curl -H "Authorization: Bearer YOUR_TOKEN" %BASE_URL%/admin/room-management/rooms

echo.
echo 5. 测试创建学校...
echo curl -X POST -H "Authorization: Bearer YOUR_TOKEN" -H "Content-Type: application/json" -d "{\"name\":\"测试大学\"}" %BASE_URL%/admin/room-management/schools

echo.
echo 6. 测试创建房间...
echo curl -X POST -H "Authorization: Bearer YOUR_TOKEN" -H "Content-Type: application/json" -d "{\"schoolId\":1,\"name\":\"测试房间\",\"roomId\":\"TEST-001\",\"totalSeats\":50,\"maxReservationHours\":8,\"description\":\"测试房间描述\"}" %BASE_URL%/admin/room-management/rooms

echo.
echo === 请按照上面的步骤手动测试 ===
pause
