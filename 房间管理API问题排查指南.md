# 房间管理API问题排查指南

## 🚨 当前问题
访问 `http://localhost:8080/api/admin/room-management/rooms` 返回404错误。

## 🔍 排查步骤

### 1. 确认后端服务状态
```bash
# 检查后端是否正在运行
curl http://localhost:8080/actuator/health
# 或者检查其他已知的API
curl http://localhost:8080/api/auth/test
```

### 2. 重启后端服务
```bash
cd backend
mvn clean compile
mvn spring-boot:run
```

### 3. 检查控制器是否被扫描
在后端启动日志中查找：
```
Mapped "{[/api/admin/room-management/rooms],methods=[GET]}"
```

### 4. 测试简单的接口
```bash
# 首先测试登录获取token
curl -X POST \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}' \
  http://localhost:8080/api/auth/login

# 使用获取的token测试控制器连通性
curl -H "Authorization: Bearer YOUR_TOKEN" \
  http://localhost:8080/api/admin/room-management/test
```

### 5. 检查权限配置
确保：
- JWT token有效
- 用户具有ADMIN角色
- Spring Security配置正确

## 🔧 可能的解决方案

### 方案1: 重新编译和启动
```bash
cd backend
mvn clean package -DskipTests
mvn spring-boot:run
```

### 方案2: 检查包扫描
确保 `AdminRoomController` 在正确的包路径下，并且被Spring Boot扫描到。

在 `SeatReservationApplication.java` 中确认：
```java
@SpringBootApplication
@MapperScan("com.seatmaster.mapper")
public class SeatReservationApplication {
    // ...
}
```

### 方案3: 临时移除权限验证
临时注释掉 `@PreAuthorize("hasRole('ADMIN')")` 来测试路由是否工作：

```java
@RestController
@RequestMapping("/api/admin/room-management")
// @PreAuthorize("hasRole('ADMIN')")  // 临时注释
public class AdminRoomController {
    // ...
}
```

### 方案4: 添加调试日志
在控制器方法中添加日志：

```java
@GetMapping("/rooms")
public Result<List<Room>> getAllRooms() {
    log.info("=== 房间管理API被调用 ===");
    // ...
}
```

### 方案5: 检查Spring Boot版本兼容性
确保所有依赖版本兼容，特别是：
- Spring Boot
- Spring Security
- MyBatis Plus

## 🧪 测试命令

### 基础连通性测试
```bash
# 测试后端是否启动
curl http://localhost:8080

# 测试已知的API
curl http://localhost:8080/api/auth/test
```

### 权限测试
```bash
# 1. 获取管理员token
TOKEN=$(curl -s -X POST \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}' \
  http://localhost:8080/api/auth/login | jq -r '.data.token')

# 2. 测试房间管理API
curl -H "Authorization: Bearer $TOKEN" \
  http://localhost:8080/api/admin/room-management/test

curl -H "Authorization: Bearer $TOKEN" \
  http://localhost:8080/api/admin/room-management/schools

curl -H "Authorization: Bearer $TOKEN" \
  http://localhost:8080/api/admin/room-management/rooms
```

## 📋 检查清单

- [ ] 后端服务正常启动
- [ ] 没有编译错误
- [ ] 控制器类在正确的包路径
- [ ] `@RestController` 和 `@RequestMapping` 注解正确
- [ ] Spring Boot能够扫描到控制器
- [ ] JWT token有效且用户是管理员
- [ ] 数据库连接正常
- [ ] 没有其他路由冲突

## 🔄 快速修复步骤

1. **重启后端服务**
   ```bash
   cd backend
   mvn spring-boot:run
   ```

2. **检查启动日志**
   查找控制器映射信息和任何错误

3. **测试基础API**
   先测试已知工作的API，再测试新的API

4. **逐步测试**
   - 测试 `/test` 端点
   - 测试 `/schools` 端点  
   - 最后测试 `/rooms` 端点

5. **查看详细错误**
   如果仍然404，检查后端日志中的详细错误信息

## 💡 提示

如果问题持续存在，可能需要：
1. 检查Spring Boot的自动配置
2. 验证MyBatis Plus的配置
3. 确认数据库表结构正确
4. 检查是否有其他配置冲突

请按照这个指南逐步排查，找到问题的根本原因。
