package com.seatmaster.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.seatmaster.common.Result;
import com.seatmaster.dto.AutoReservationRequest;
import com.seatmaster.dto.ExecutionResult;
import com.seatmaster.entity.AutoExecutionLog;
import com.seatmaster.entity.Reservation;
import com.seatmaster.entity.Room;
import com.seatmaster.entity.User;
import com.seatmaster.mapper.AutoExecutionLogMapper;
import com.seatmaster.service.AutoReservationSchedulerService;
import com.seatmaster.service.ReservationService;
import com.seatmaster.service.UserService;
import com.seatmaster.mapper.RoomMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;

@RestController
@RequestMapping("/api/reservations")
@CrossOrigin
public class ReservationController {
    
    @Autowired
    private ReservationService reservationService;

    @Autowired
    private RoomMapper roomMapper;

    @Autowired
    private UserService userService;

    @Autowired
    private AutoReservationSchedulerService autoReservationSchedulerService;

    @Autowired
    private AutoExecutionLogMapper autoExecutionLogMapper;
    
    /**
     * 智能解析时间字符串，支持多种格式
     * @param timeStr 时间字符串，可能是 HH:MM:SS, HH:MM 或 ISO 8601 格式
     * @return LocalTime对象
     */
    private LocalTime parseTimeString(String timeStr) {
        try {
            // 尝试直接解析为时间格式
            if (timeStr.matches("^\\d{2}:\\d{2}(:\\d{2})?$")) {
                return LocalTime.parse(timeStr);
            }
            
            // 尝试解析为ISO 8601日期时间格式，提取时间部分
            if (timeStr.contains("T")) {
                LocalDateTime dateTime = LocalDateTime.parse(timeStr);
                return dateTime.toLocalTime();
            }
            
            // 如果包含日期，尝试其他格式
            if (timeStr.contains("-") || timeStr.contains("/")) {
                // 可能的格式：yyyy-MM-dd HH:mm:ss 或类似
                try {
                    LocalDateTime dateTime = LocalDateTime.parse(timeStr, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                    return dateTime.toLocalTime();
                } catch (Exception e) {
                    // 继续尝试其他格式
                }
            }
            
            // 最后尝试直接解析
            return LocalTime.parse(timeStr);
            
        } catch (Exception e) {
            throw new RuntimeException("无法解析时间格式: " + timeStr + "。支持的格式: HH:MM, HH:MM:SS, 或 ISO 8601 (yyyy-MM-ddTHH:mm:ss)");
        }
    }
    
    /**
     * 创建或更新预约
     */
    @PostMapping("/create")
    public Result<?> createReservation(@RequestParam Long roomId,
                                     @RequestParam String seatId,
                                     @RequestParam String startTime,
                                     @RequestParam String endTime,
                                     @RequestParam(required = false, defaultValue = "") String reservationOpenTime,
                                     @RequestParam(required = false, defaultValue = "SAME_DAY") String reservationType,
                                     Authentication authentication) {
        try {
            // 获取当前用户
            String username = authentication.getName();
            User currentUser = userService.findByUsername(username);
            
            if (currentUser == null) {
                return Result.error("用户不存在");
            }
            
            // 智能解析时间，支持多种格式
            LocalTime start = parseTimeString(startTime);
            LocalTime end = parseTimeString(endTime);
            
            // 创建或更新预约
            Reservation reservation = reservationService.createOrUpdateReservation(
                currentUser.getId(), roomId, seatId, start, end, reservationOpenTime, reservationType);
                
            return Result.success("预约成功", reservation);
            
        } catch (Exception e) {
            return Result.error("预约失败: " + e.getMessage());
        }
    }
    
    /**
     * 取消预约
     */
    @PostMapping("/cancel/{reservationId}")
    public Result<?> cancelReservation(@PathVariable Long reservationId, Authentication authentication) {
        try {
            String username = authentication.getName();
            User currentUser = userService.findByUsername(username);
            
            if (currentUser == null) {
                return Result.error("用户不存在");
            }
            
            boolean success = reservationService.cancelReservation(reservationId, currentUser.getId());
            
            if (success) {
                return Result.success("取消预约成功");
            } else {
                return Result.error("取消预约失败");
            }
            
        } catch (Exception e) {
            return Result.error("取消预约失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取房间列表及可用座位信息
     */
    @GetMapping("/rooms")
    public Result<?> getRooms() {
        try {
            List<Room> rooms = roomMapper.findAllWithAvailableSeats();
            return Result.success("获取房间列表成功", rooms);
        } catch (Exception e) {
            return Result.error("获取房间列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取指定房间的可用座位数
     */
    @GetMapping("/rooms/{roomId}/available-seats")
    public Result<?> getAvailableSeats(@PathVariable Long roomId,
                                     @RequestParam String startTime,
                                     @RequestParam String endTime) {
        try {
            // 智能解析时间，支持多种格式
            LocalTime start = parseTimeString(startTime);
            LocalTime end = parseTimeString(endTime);
            
            int availableSeats = reservationService.getAvailableSeatsCount(roomId, start, end);
            
            return Result.success("获取可用座位数成功", availableSeats);
        } catch (Exception e) {
            return Result.error("获取可用座位数失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取房间活跃预约列表
     */
    @GetMapping("/rooms/{roomId}/active")
    public Result<?> getActiveReservations(@PathVariable Long roomId) {
        try {
            List<Reservation> reservations = reservationService.getActiveReservations(roomId);
            return Result.success("获取活跃预约列表成功", reservations);
        } catch (Exception e) {
            return Result.error("获取活跃预约列表失败: " + e.getMessage());
        }
    }

    // ========== 自动预约相关API ==========

    /**
     * 创建自动预约
     */
    @PostMapping("/auto")
    public Result<?> createAutoReservation(@Valid @RequestBody AutoReservationRequest request,
                                         Authentication authentication) {
        try {
            // 获取当前用户
            String username = authentication.getName();
            User currentUser = userService.findByUsername(username);

            if (currentUser == null) {
                return Result.error("用户不存在");
            }

            // 检查用户剩余天数
            if (currentUser.getRemainingDays() <= 0) {
                return Result.error("剩余天数不足，无法创建自动预约");
            }

            // 验证时间格式
            if (!request.isValidTimeFormat()) {
                return Result.error("时间格式错误，结束时间必须晚于开始时间");
            }

            // 创建自动预约记录
            Reservation reservation = new Reservation();
            reservation.setUserId(currentUser.getId());
            reservation.setRoomId(request.getRoomId());
            reservation.setSeatId(request.getSeatId());
            reservation.setStartTime(parseTimeString(request.getStartTime()));
            reservation.setEndTime(parseTimeString(request.getEndTime()));
            reservation.setStatus("AUTO_PENDING");
            reservation.setReservationOpenTime(request.getReservationOpenTime());
            reservation.setReservationType(request.getReservationType());
            reservation.setCreatedTime(LocalDateTime.now());

            boolean success = reservationService.save(reservation);

            if (success) {
                return Result.success("自动预约创建成功", reservation);
            } else {
                return Result.error("自动预约创建失败");
            }

        } catch (Exception e) {
            return Result.error("创建失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户的自动预约列表
     */
    @GetMapping("/auto")
    public Result<?> getUserAutoReservations(Authentication authentication) {
        try {
            String username = authentication.getName();
            User currentUser = userService.findByUsername(username);

            if (currentUser == null) {
                return Result.error("用户不存在");
            }

            List<Reservation> autoReservations = reservationService.list(
                new QueryWrapper<Reservation>()
                    .eq("user_id", currentUser.getId())
                    .in("status", Arrays.asList("AUTO_PENDING", "AUTO_SUCCESS", "AUTO_FAILED"))
                    .orderByDesc("created_time")
            );

            return Result.success("获取自动预约列表成功", autoReservations);

        } catch (Exception e) {
            return Result.error("获取自动预约列表失败: " + e.getMessage());
        }
    }

    /**
     * 手动触发自动预约
     */
    @PostMapping("/auto/{reservationId}/execute")
    public Result<?> manualExecuteAutoReservation(@PathVariable Long reservationId,
                                                 Authentication authentication) {
        try {
            String username = authentication.getName();
            User currentUser = userService.findByUsername(username);

            if (currentUser == null) {
                return Result.error("用户不存在");
            }

            ExecutionResult result = autoReservationSchedulerService.manualExecuteReservation(
                reservationId, currentUser.getId());

            if (result.isSuccess()) {
                return Result.success(result.getMessage());
            } else {
                return Result.error(result.getMessage());
            }

        } catch (Exception e) {
            return Result.error("执行失败: " + e.getMessage());
        }
    }

    /**
     * 获取执行日志
     */
    @GetMapping("/auto/{reservationId}/logs")
    public Result<?> getExecutionLogs(@PathVariable Long reservationId,
                                    Authentication authentication) {
        try {
            String username = authentication.getName();
            User currentUser = userService.findByUsername(username);

            if (currentUser == null) {
                return Result.error("用户不存在");
            }

            // 验证预约记录属于当前用户
            Reservation reservation = reservationService.getOne(
                new QueryWrapper<Reservation>()
                    .eq("id", reservationId)
                    .eq("user_id", currentUser.getId())
            );

            if (reservation == null) {
                return Result.error("预约记录不存在或无权访问");
            }

            List<AutoExecutionLog> logs = autoExecutionLogMapper.selectByReservationId(reservationId);

            return Result.success("获取执行日志成功", logs);

        } catch (Exception e) {
            return Result.error("获取执行日志失败: " + e.getMessage());
        }
    }

    /**
     * 删除自动预约
     */
    @DeleteMapping("/auto/{reservationId}")
    public Result<?> deleteAutoReservation(@PathVariable Long reservationId,
                                         Authentication authentication) {
        try {
            String username = authentication.getName();
            User currentUser = userService.findByUsername(username);

            if (currentUser == null) {
                return Result.error("用户不存在");
            }

            Reservation reservation = reservationService.getOne(
                new QueryWrapper<Reservation>()
                    .eq("id", reservationId)
                    .eq("user_id", currentUser.getId())
                    .eq("status", "AUTO_PENDING")
            );

            if (reservation == null) {
                return Result.error("自动预约记录不存在或无法删除");
            }

            boolean success = reservationService.removeById(reservationId);

            if (success) {
                return Result.success("自动预约删除成功");
            } else {
                return Result.error("删除失败");
            }

        } catch (Exception e) {
            return Result.error("删除失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户自动预约统计信息
     */
    @GetMapping("/auto/statistics")
    public Result<?> getUserAutoReservationStatistics(@RequestParam(defaultValue = "7") Integer days,
                                                     Authentication authentication) {
        try {
            String username = authentication.getName();
            User currentUser = userService.findByUsername(username);

            if (currentUser == null) {
                return Result.error("用户不存在");
            }

            ExecutionResult result = autoReservationSchedulerService.getUserStatistics(
                currentUser.getId(), days);

            if (result.isSuccess()) {
                return Result.success("获取统计信息成功", result.getDetails());
            } else {
                return Result.error(result.getMessage());
            }

        } catch (Exception e) {
            return Result.error("获取统计信息失败: " + e.getMessage());
        }
    }
}