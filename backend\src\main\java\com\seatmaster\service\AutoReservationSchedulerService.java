package com.seatmaster.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.seatmaster.dto.ExecutionResult;
import com.seatmaster.entity.AutoExecutionLog;
import com.seatmaster.entity.Reservation;
import com.seatmaster.entity.User;
import com.seatmaster.mapper.AutoExecutionLogMapper;
import com.seatmaster.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;

/**
 * 自动预约调度服务
 * 负责定时扫描和执行自动预约任务
 */
@Service
@Slf4j
public class AutoReservationSchedulerService {
    
    @Autowired
    private ReservationService reservationService;
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private XuexitongApiService xuexitongApiService;
    
    @Autowired
    private AutoExecutionLogMapper autoExecutionLogMapper;
    
    @Value("${auto-reservation.enabled:true}")
    private boolean autoReservationEnabled;
    
    @Value("${auto-reservation.scheduler.max-concurrent:5}")
    private int maxConcurrent;
    
    // 线程池，用于并发执行预约任务
    private final Executor taskExecutor = Executors.newFixedThreadPool(10);
    
    /**
     * 定时任务：每分钟扫描并执行自动预约
     * 
     * 执行时间：每分钟的第0秒
     */
    @Scheduled(cron = "0 * * * * ?")
    public void scanAndExecuteAutoReservations() {
        if (!autoReservationEnabled) {
            log.debug("自动预约功能已禁用");
            return;
        }
        
        try {
            log.info("开始扫描自动预约任务...");
            
            // 1. 查询需要执行的自动预约
            List<Reservation> pendingReservations = findPendingReservations();
            
            if (pendingReservations.isEmpty()) {
                log.debug("没有找到待执行的自动预约任务");
                return;
            }
            
            log.info("找到 {} 个待执行的自动预约任务", pendingReservations.size());
            
            // 2. 并发执行预约任务
            int executedCount = 0;
            for (Reservation reservation : pendingReservations) {
                if (executedCount >= maxConcurrent) {
                    log.warn("达到最大并发数限制 {}，剩余 {} 个任务将在下次执行", 
                        maxConcurrent, pendingReservations.size() - executedCount);
                    break;
                }
                
                CompletableFuture.runAsync(() -> {
                    executeAutoReservation(reservation);
                }, taskExecutor);
                
                executedCount++;
            }
            
            log.info("已提交 {} 个自动预约任务执行", executedCount);
            
        } catch (Exception e) {
            log.error("扫描自动预约任务异常", e);
        }
    }
    
    /**
     * 查询待执行的自动预约
     * 
     * @return 待执行的预约列表
     */
    private List<Reservation> findPendingReservations() {
        LocalTime currentTime = LocalTime.now();
        String currentTimeStr = currentTime.format(DateTimeFormatter.ofPattern("HH:mm:ss"));
        
        log.debug("查询当前时间 {} 的待执行预约", currentTimeStr);
        
        return reservationService.list(
            new QueryWrapper<Reservation>()
                .eq("status", "AUTO_PENDING")
                .eq("reservation_type", "ADVANCE_ONE_DAY")
                .eq("reservation_open_time", currentTimeStr)
                .and(wrapper -> wrapper
                    .isNull("last_execution_time")
                    .or()
                    .lt("DATE(last_execution_time)", LocalDate.now())
                )
        );
    }
    
    /**
     * 执行单个自动预约任务
     * 
     * @param reservation 预约信息
     */
    private void executeAutoReservation(Reservation reservation) {
        long startTime = System.currentTimeMillis();
        
        try {
            log.info("开始执行自动预约: reservationId={}, userId={}, roomId={}, seatId={}", 
                reservation.getId(), reservation.getUserId(), reservation.getRoomId(), reservation.getSeatId());
            
            // 1. 检查用户状态
            User user = userService.getById(reservation.getUserId());
            if (user == null) {
                log.warn("用户 {} 不存在，跳过自动预约 {}", 
                    reservation.getUserId(), reservation.getId());
                
                updateReservationStatus(reservation, ExecutionResult.failure("用户不存在"));
                return;
            }
            
            if (user.getRemainingDays() <= 0) {
                log.warn("用户 {} 剩余天数不足，跳过自动预约 {}", 
                    reservation.getUserId(), reservation.getId());
                
                updateReservationStatus(reservation, ExecutionResult.failure("用户剩余天数不足"));
                return;
            }
            
            // 2. 更新状态为执行中（通过添加执行时间标记）
            reservation.setLastExecutionTime(LocalDateTime.now());
            reservationService.updateById(reservation);
            
            // 3. 调用学习通API执行预约
            ExecutionResult result = xuexitongApiService.executeReservation(reservation);
            
            // 4. 更新预约结果
            updateReservationStatus(reservation, result);
            
            // 5. 记录执行日志
            recordExecutionLog(reservation, result, startTime);
            
            log.info("自动预约执行完成: reservationId={}, success={}, message={}", 
                reservation.getId(), result.isSuccess(), result.getMessage());
            
        } catch (Exception e) {
            log.error("自动预约执行异常: reservationId={}, error={}", 
                reservation.getId(), e.getMessage(), e);
            
            ExecutionResult errorResult = ExecutionResult.failure("执行异常: " + e.getMessage());
            updateReservationStatus(reservation, errorResult);
            recordExecutionLog(reservation, errorResult, startTime);
        }
    }
    
    /**
     * 更新预约状态
     * 
     * @param reservation 预约信息
     * @param result 执行结果
     */
    private void updateReservationStatus(Reservation reservation, ExecutionResult result) {
        try {
            reservation.setStatus(result.isSuccess() ? "AUTO_SUCCESS" : "AUTO_FAILED");
            reservation.setExecutionResult(JsonUtils.toJson(result));
            reservation.setLastExecutionTime(LocalDateTime.now());
            
            reservationService.updateById(reservation);
            
            log.debug("更新预约状态成功: reservationId={}, status={}", 
                reservation.getId(), reservation.getStatus());
            
        } catch (Exception e) {
            log.error("更新预约状态失败: reservationId={}, error={}", 
                reservation.getId(), e.getMessage(), e);
        }
    }
    
    /**
     * 记录执行日志
     * 
     * @param reservation 预约信息
     * @param result 执行结果
     * @param startTime 开始时间
     */
    private void recordExecutionLog(Reservation reservation, ExecutionResult result, long startTime) {
        try {
            AutoExecutionLog executionLog = new AutoExecutionLog();
            executionLog.setReservationId(reservation.getId());
            executionLog.setUserId(reservation.getUserId());
            executionLog.setExecutionTime(LocalDateTime.now());
            executionLog.setExecutionStatus(result.isSuccess() ? "SUCCESS" : "FAILED");
            executionLog.setResultMessage(result.getMessage());
            executionLog.setExecutionDuration((int) ((System.currentTimeMillis() - startTime) / 1000));
            
            autoExecutionLogMapper.insert(executionLog);
            
            log.debug("记录执行日志成功: reservationId={}, status={}, duration={}s", 
                reservation.getId(), executionLog.getExecutionStatus(), executionLog.getExecutionDuration());
            
        } catch (Exception e) {
            log.error("记录执行日志失败: reservationId={}, error={}", 
                reservation.getId(), e.getMessage(), e);
        }
    }
    
    /**
     * 手动触发自动预约 (供API调用)
     * 
     * @param reservationId 预约ID
     * @param userId 用户ID
     * @return 执行结果
     */
    public ExecutionResult manualExecuteReservation(Long reservationId, Long userId) {
        try {
            log.info("手动触发自动预约: reservationId={}, userId={}", reservationId, userId);
            
            Reservation reservation = reservationService.getOne(
                new QueryWrapper<Reservation>()
                    .eq("id", reservationId)
                    .eq("user_id", userId)
                    .eq("status", "AUTO_PENDING")
            );
            
            if (reservation == null) {
                return ExecutionResult.failure("预约记录不存在或状态不正确");
            }
            
            // 异步执行
            CompletableFuture.runAsync(() -> {
                executeAutoReservation(reservation);
            }, taskExecutor);
            
            return ExecutionResult.success("手动执行已启动", "预约任务已提交执行");
            
        } catch (Exception e) {
            log.error("手动执行自动预约失败: reservationId={}, userId={}, error={}", 
                reservationId, userId, e.getMessage(), e);
            return ExecutionResult.failure("手动执行失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取用户的自动预约统计信息
     * 
     * @param userId 用户ID
     * @param days 统计天数
     * @return 统计信息
     */
    public ExecutionResult getUserStatistics(Long userId, Integer days) {
        try {
            // 获取成功率
            Double successRate = autoExecutionLogMapper.getSuccessRateByUserId(userId, days);
            
            // 获取最近的执行日志
            List<AutoExecutionLog> recentLogs = autoExecutionLogMapper.selectByUserId(userId, 10);
            
            Map<String, Object> statistics = new HashMap<>();
            statistics.put("successRate", successRate != null ? successRate : 0.0);
            statistics.put("recentLogs", recentLogs);
            statistics.put("statisticsDays", days);
            
            return ExecutionResult.success("获取统计信息成功", JsonUtils.toJson(statistics));
            
        } catch (Exception e) {
            log.error("获取用户统计信息失败: userId={}, error={}", userId, e.getMessage(), e);
            return ExecutionResult.failure("获取统计信息失败: " + e.getMessage());
        }
    }
}
