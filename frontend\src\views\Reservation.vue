<template>
  <div class="reservation-container">
    <el-container>
      <el-header class="reservation-header">
        <div class="header-left">
          <el-button type="info" plain @click="goBack">
            <el-icon><ArrowLeft /></el-icon>
            返回首页
          </el-button>
          <h2>座位预约</h2>
        </div>
      </el-header>
      
      <el-main class="reservation-main">
        <div class="reservation-content">
          <el-row :gutter="30">
            <el-col :span="16">
              <el-card class="form-card">
                <template #header>
                  <span>预约信息设置</span>
                </template>
                
                <el-form
                  ref="reservationFormRef"
                  :model="reservationForm"
                  :rules="reservationRules"
                  label-width="120px"
                  class="reservation-form"
                >
                  <el-form-item label="选择学校" prop="schoolId">
                    <el-select
                      v-model="reservationForm.schoolId"
                      placeholder="请选择学校"
                      @change="onSchoolChange"
                      :loading="schoolsLoading"
                      style="width: 100%"
                    >
                      <el-option
                        v-for="school in schools"
                        :key="school.id"
                        :label="school.name"
                        :value="school.id"
                      />
                    </el-select>
                  </el-form-item>
                  
                  <el-form-item label="选择房间" prop="roomId">
                    <el-select
                      v-model="reservationForm.roomId"
                      placeholder="请先选择学校"
                      :disabled="!reservationForm.schoolId"
                      :loading="roomsLoading"
                      style="width: 100%"
                      @change="onRoomChange"
                    >
                      <el-option
                        v-for="room in rooms"
                        :key="room.id"
                        :label="room.name"
                        :value="room.id"
                      >
                        <span>{{ room.name }}</span>
                      </el-option>
                    </el-select>
                  </el-form-item>
                  
                  <el-form-item label="座位号" prop="seatId">
                    <el-input
                      v-model="reservationForm.seatId"
                      placeholder="请输入座位号（如：A01、B15等）"
                      style="width: 100%"
                    />
                  </el-form-item>
                  
                  <el-form-item label="预约开放时间" prop="reservationOpenTime">
                    <el-time-select
                      v-model="reservationForm.reservationOpenTime"
                      placeholder="选择开放时间点"
                      start="00:00"
                      step="00:15"
                      end="23:45"
                      style="width: 100%;"
                      @change="handleReservationOpenTimeChange"
                      :clearable="true"
                    />
                    <div style="font-size: 12px; color: #909399; margin-top: 4px;">
                      选择预约开放的具体时间点（15分钟间隔）
                    </div>
                  </el-form-item>
                  
                  <el-form-item label="预约类型" prop="reservationType">
                    <el-select
                      v-model="reservationForm.reservationType"
                      placeholder="请选择预约类型"
                      style="width: 100%"
                    >
                      <el-option label="当天预约" value="SAME_DAY" />
                      <el-option label="提前一天预约" value="ADVANCE_ONE_DAY" />
                    </el-select>
                    <div style="font-size: 12px; color: #909399; margin-top: 4px;">
                      选择是当天预约还是提前一天预约
                    </div>
                  </el-form-item>
                  
                  <el-form-item label="开始时间" prop="startTime">
                    <el-time-select
                      v-model="reservationForm.startTime"
                      placeholder="选择开始时间"
                      start="00:00"
                      step="00:15"
                      end="23:45"
                      style="width: 100%;"
                      @change="handleStartTimeChange"
                      :clearable="true"
                    />
                    <div style="font-size: 12px; color: #909399; margin-top: 4px;">
                      15分钟间隔选择（如 08:00、08:15、08:30、08:45）
                    </div>
                  </el-form-item>

                  <el-form-item label="结束时间" prop="endTime">
                    <el-time-select
                      v-model="reservationForm.endTime"
                      placeholder="选择结束时间"
                      start="00:00"
                      step="00:15"
                      end="23:45"
                      style="width: 100%;"
                      @change="handleEndTimeChange"
                      :clearable="true"
                    />
                    <div style="font-size: 12px; color: #909399; margin-top: 4px;">
                      15分钟间隔选择（如 08:00、08:15、08:30、08:45）
                    </div>
                  </el-form-item>
                  
                  <el-form-item>
                    <el-button
                      type="primary"
                      @click="submitReservation"
                      :loading="submitting"
                      :disabled="userProfile && userProfile.remainingDays <= 0"
                      size="large"
                    >
                      {{ hasExistingReservation ? '更新预约' : '确认预约' }}
                    </el-button>
                    <el-button
                      type="info"
                      @click="refreshTimeDisplay"
                      size="large"
                      style="margin-left: 10px;"
                    >
                      刷新时间显示
                    </el-button>
                  </el-form-item>
                </el-form>
              </el-card>
            </el-col>
            
            <el-col :span="8">
              <el-card class="info-card">
                <template #header>
                  <span>预约信息</span>
                </template>

                <!-- 用户剩余天数显示 -->
                <div class="info-section" v-if="userProfile">
                  <h4>剩余天数</h4>
                  <el-tag
                    :type="getRemainingDaysType(userProfile.remainingDays)"
                    size="large"
                  >
                    {{ userProfile.remainingDays !== undefined ? userProfile.remainingDays : 0 }} 天
                  </el-tag>
                  <div v-if="userProfile.remainingDays <= 0" style="color: #f56c6c; font-size: 12px; margin-top: 4px;">
                    剩余天数不足，无法进行预约
                  </div>
                </div>
                
                <div class="info-section" v-if="selectedSchool">
                  <h4>选择的学校</h4>
                  <p>{{ selectedSchool.name }}</p>
                </div>
                
                <div class="info-section" v-if="selectedRoom">
                  <h4>选择的房间</h4>
                  <p>{{ selectedRoom.name }}</p>
                </div>
                
                <div class="info-section" v-if="reservationForm.seatId">
                  <h4>选择的座位</h4>
                  <p>{{ reservationForm.seatId }}</p>
                </div>
                
                <div class="info-section" v-if="reservationForm.reservationOpenTime">
                  <h4>预约开放时间</h4>
                  <p>{{ reservationForm.reservationOpenTime.replace(':00', '') }}</p>
                </div>
                
                <div class="info-section" v-if="reservationForm.reservationType">
                  <h4>预约类型</h4>
                  <p>{{ reservationForm.reservationType === 'SAME_DAY' ? '当天预约' : '提前一天预约' }}</p>
                </div>
                
                <div class="info-section" v-if="combinedStartTime">
                  <h4>开始时间</h4>
                  <p>{{ combinedStartTime.replace(':00', '') }}</p>
                </div>
                
                <div class="info-section" v-if="combinedEndTime">
                  <h4>结束时间</h4>
                  <p>{{ combinedEndTime.replace(':00', '') }}</p>
                </div>
                
                <div class="info-section" v-if="reservationDuration">
                  <h4>预约时长</h4>
                  <p>{{ reservationDuration }}</p>
                </div>
                
                <div class="info-section">
                  <h4>预约须知</h4>
                  <ul class="rules-list">
                    <li>如已有预约则为修改预约，否则创建新预约</li>
                    <li>座位号由用户自选，请确保座位号准确</li>
                    <li>预约时间间隔至少需要15分钟</li>
                    <li>时间选择为下拉菜单，15分钟间隔（如7:00、7:15、7:30、7:45等）</li>
                    <li>预约开放时间为下拉菜单选择，可选择具体开放时间点</li>
                    <li>预约类型：可选择当天预约或提前一天预约</li>
                    <li>开始时间和结束时间指一天内的时间段</li>
                    <li>请在预约时间内到达指定房间和座位</li>
                    <li>可在预约开始前取消预约</li>
                    <li>预约成功后将在个人信息页面显示详情</li>
                  </ul>
                </div>
                
                <!-- 调试信息面板 -->
                <div class="info-section" style="background-color: #f8f9fa; border: 1px solid #e9ecef; padding: 10px; border-radius: 4px;">
                  <h4 style="color: #6c757d;">调试信息</h4>
                  <div style="font-size: 12px; color: #6c757d;">
                    <p>组合预约开放时间 (computed): {{ combinedReservationOpenTime }}</p>
                    <p>组合开始时间 (computed): {{ combinedStartTime }}</p>
                    <p>组合结束时间 (computed): {{ combinedEndTime }}</p>
                    <p>表单 - 预约开放时间 (form value): {{ reservationForm.reservationOpenTime }}</p>
                    <p>表单 - 开始时间 (form value): {{ reservationForm.startTime }}</p>
                    <p>表单 - 结束时间 (form value): {{ reservationForm.endTime }}</p>
                  </div>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>
      </el-main>
    </el-container>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowLeft } from '@element-plus/icons-vue'
import api from '@/utils/api'
import { ElForm, ElFormItem, ElInput, ElSelect, ElOption, ElButton, ElRow, ElCol, ElCard, ElContainer, ElHeader, ElMain, ElIcon, ElTimePicker, ElTimeSelect } from 'element-plus'

const router = useRouter()

const schools = ref([])
const rooms = ref([])
const schoolsLoading = ref(false)
const roomsLoading = ref(false)
const submitting = ref(false)
const hasExistingReservation = ref(false)
const currentReservation = ref(null)
const userProfile = ref(null)

const reservationFormRef = ref(null)

const reservationForm = ref({
  schoolId: null,
  roomId: null,
  seatId: '',
  reservationOpenTime: '',
  reservationType: 'SAME_DAY',
  startTime: '',
  endTime: '',
})

// --- Time Change Handlers ---
const handleReservationOpenTimeChange = (newTime) => {
  try {
    console.log("Reservation Open Time changed:", newTime)
    // el-time-select 返回 HH:mm 格式，转换为 HH:mm:ss
    const normalizedTime = newTime ? (newTime.length === 5 ? newTime + ':00' : newTime) : ''
    reservationForm.value.reservationOpenTime = normalizedTime
  } catch (e) {
    console.error('Error in handleReservationOpenTimeChange:', e)
    ElMessage.warning('预约开放时间格式有误，请重新选择')
    reservationForm.value.reservationOpenTime = ''
  }
}

const handleStartTimeChange = (newTime) => {
  try {
    console.log("Start Time changed:", newTime, typeof newTime)

    // el-time-select 返回 HH:mm 格式，转换为 HH:mm:ss
    const normalizedTime = newTime ? (newTime.length === 5 ? newTime + ':00' : newTime) : ''
    console.log("Normalized start time:", normalizedTime)

    reservationForm.value.startTime = normalizedTime

    // 即时验证时间
    validateTimeImmediately('start')

    if (reservationFormRef.value) {
      // 使用 nextTick 确保值已更新
      nextTick(() => {
        try {
          reservationFormRef.value?.validateField('startTime')
          reservationFormRef.value?.validateField('endTime')
        } catch (fieldValidationError) {
          console.warn('字段验证过程中出现错误:', fieldValidationError)
        }
      }).catch(error => {
        console.warn('nextTick 执行过程中出现错误:', error)
      })
    }
  } catch (e) {
    console.error('Error in handleStartTimeChange:', e)
    ElMessage.warning('开始时间格式有误，请重新选择')
    reservationForm.value.startTime = ''
  }
}

const handleEndTimeChange = (newTime) => {
  try {
    console.log("End Time changed:", newTime, typeof newTime)

    // el-time-select 返回 HH:mm 格式，转换为 HH:mm:ss
    const normalizedTime = newTime ? (newTime.length === 5 ? newTime + ':00' : newTime) : ''
    console.log("Normalized end time:", normalizedTime)

    reservationForm.value.endTime = normalizedTime

    // 即时验证时间
    validateTimeImmediately('end')

    if (reservationFormRef.value) {
      // 使用 nextTick 确保值已更新
      nextTick(() => {
        try {
          reservationFormRef.value?.validateField('endTime')
          reservationFormRef.value?.validateField('startTime')
        } catch (fieldValidationError) {
          console.warn('字段验证过程中出现错误:', fieldValidationError)
        }
      }).catch(error => {
        console.warn('nextTick 执行过程中出现错误:', error)
      })
    }
  } catch (e) {
    console.error('Error in handleEndTimeChange:', e)
    ElMessage.warning('结束时间格式有误，请重新选择')
    reservationForm.value.endTime = ''
  }
}

// 即时时间验证函数
let validationTimeout = null
const validateTimeImmediately = (changedField) => {
  try {
    // 清除之前的延时提示
    if (validationTimeout) {
      clearTimeout(validationTimeout)
    }

    // 延时验证，避免用户选择过程中频繁提示
    validationTimeout = setTimeout(() => {
      try {
        performTimeValidation(changedField)
      } catch (validationError) {
        console.warn('时间验证执行过程中出现错误:', validationError)
        handleGlobalError(validationError, '时间验证')
      }
    }, 500) // 500ms延时

  } catch (error) {
    console.error('Immediate validation setup error:', error)
    handleGlobalError(error, '时间验证设置')
  }
}

// 执行实际的时间验证
const performTimeValidation = (changedField) => {
  try {
    const start = normalizeTimeValue(reservationForm.value.startTime)
    const end = normalizeTimeValue(reservationForm.value.endTime)
    
    // 如果时间不完整，不进行验证
    if (!start || !end) return
    
    // 验证时间格式
    const timeRegex = /^\d{2}:\d{2}:\d{2}$/
    if (!timeRegex.test(start) || !timeRegex.test(end)) {
      ElMessage.warning('时间格式不正确，请重新选择')
      return
    }
    
    let startDate, endDate
    try {
      startDate = new Date(`2000/01/01 ${start}`)
      endDate = new Date(`2000/01/01 ${end}`)
    } catch (dateError) {
      ElMessage.warning('时间解析失败，请重新选择')
      return
    }
    
    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
      ElMessage.warning('时间格式无效，请重新选择')
      return
    }
    
    // 检查时间顺序
    if (startDate >= endDate) {
      ElMessage({
        message: '结束时间必须在开始时间之后，请调整时间选择',
        type: 'warning',
        duration: 3000,
        showClose: true
      })
      return
    }
    
    // 检查时间间隔
    const diffMinutes = (endDate.getTime() - startDate.getTime()) / (1000 * 60)
    if (diffMinutes < 15) {
      ElMessage({
        message: '预约时间间隔至少需要15分钟，请调整时间选择',
        type: 'warning',
        duration: 3000,
        showClose: true
      })
      return
    }
    
    // 验证通过时可以显示成功提示（可选）
    console.log('Time validation passed:', { start, end, duration: `${Math.floor(diffMinutes / 60)}小时${diffMinutes % 60}分钟` })
    
  } catch (error) {
    console.error('Time validation error:', error)
    ElMessage.warning('时间验证出现问题，请检查时间选择')
  }
}

// 时间值标准化函数
const normalizeTimeValue = (timeValue) => {
  try {
    // null 或 undefined 检查
    if (timeValue === null || timeValue === undefined) {
      return ''
    }

    // 空字符串检查
    if (timeValue === '') {
      return ''
    }
    
    // 如果是数组，取第一个元素
    if (Array.isArray(timeValue)) {
      console.log('Time value is array:', timeValue)
      if (timeValue.length === 0) {
        return ''
      }

      // 检查数组中的第一个元素
      const firstElement = timeValue[0]

      // 如果第一个元素是对象，尝试提取时间信息
      if (firstElement && typeof firstElement === 'object' && !(firstElement instanceof Date)) {
        console.warn('Array contains object, attempting to extract time:', firstElement)

        // 尝试从对象中提取时间相关属性
        if (firstElement.value) {
          return normalizeTimeValue(firstElement.value)
        }
        if (firstElement.time) {
          return normalizeTimeValue(firstElement.time)
        }
        if (firstElement.hours !== undefined && firstElement.minutes !== undefined) {
          const hours = String(firstElement.hours).padStart(2, '0')
          const minutes = String(firstElement.minutes).padStart(2, '0')
          const seconds = String(firstElement.seconds || 0).padStart(2, '0')
          return `${hours}:${minutes}:${seconds}`
        }

        // 如果无法提取，返回空字符串
        console.warn('Cannot extract time from object:', firstElement)
        return ''
      }

      // 递归处理数组中的第一个元素
      return normalizeTimeValue(firstElement)
    }
    
    // 如果是字符串，验证格式并返回
    if (typeof timeValue === 'string') {
      // 检查是否为有效的时间格式
      const timeRegex = /^\d{1,2}:\d{2}(:\d{2})?$/
      if (timeRegex.test(timeValue)) {
        // 确保格式为 HH:mm:ss
        if (timeValue.length === 5) { // HH:mm
          return timeValue + ':00'
        } else if (timeValue.length === 8) { // HH:mm:ss
          return timeValue
        }
      }
      // 如果格式不正确，尝试清理
      const cleaned = timeValue.trim()
      if (cleaned && /^\d{1,2}:\d{2}/.test(cleaned)) {
        const parts = cleaned.split(':')
        if (parts.length >= 2) {
          const hours = parts[0].padStart(2, '0')
          const minutes = parts[1].padStart(2, '0')
          const seconds = parts[2] ? parts[2].padStart(2, '0') : '00'
          return `${hours}:${minutes}:${seconds}`
        }
      }
      return timeValue // 返回原始字符串，让上层处理
    }
    
    // 如果是Date对象，转换为HH:mm:ss格式
    if (timeValue instanceof Date && !isNaN(timeValue.getTime())) {
      const hours = timeValue.getHours().toString().padStart(2, '0')
      const minutes = timeValue.getMinutes().toString().padStart(2, '0')
      const seconds = timeValue.getSeconds().toString().padStart(2, '0')
      return `${hours}:${minutes}:${seconds}`
    }
    
    // 如果是数字，假设是时间戳或毫秒数
    if (typeof timeValue === 'number' && !isNaN(timeValue)) {
      const date = new Date(timeValue)
      if (!isNaN(date.getTime())) {
        const hours = date.getHours().toString().padStart(2, '0')
        const minutes = date.getMinutes().toString().padStart(2, '0')
        const seconds = date.getSeconds().toString().padStart(2, '0')
        return `${hours}:${minutes}:${seconds}`
      }
    }
    
    // 如果是普通对象，尝试提取时间信息
    if (timeValue && typeof timeValue === 'object' && timeValue.constructor === Object) {
      console.warn('Time value is object, attempting to extract time:', timeValue)

      // 尝试从对象中提取时间相关属性
      if (timeValue.value) {
        return normalizeTimeValue(timeValue.value)
      }
      if (timeValue.time) {
        return normalizeTimeValue(timeValue.time)
      }
      if (timeValue.hours !== undefined && timeValue.minutes !== undefined) {
        const hours = String(timeValue.hours).padStart(2, '0')
        const minutes = String(timeValue.minutes).padStart(2, '0')
        const seconds = String(timeValue.seconds || 0).padStart(2, '0')
        return `${hours}:${minutes}:${seconds}`
      }

      // 如果对象有toString方法且不是默认的[object Object]
      const stringValue = String(timeValue)
      if (stringValue !== '[object Object]') {
        return normalizeTimeValue(stringValue)
      }

      console.warn('Cannot extract time from object:', timeValue)
      return ''
    }

    // 其他情况尝试转换为字符串
    const stringValue = String(timeValue)
    if (stringValue !== '[object Object]' && stringValue !== 'undefined' && stringValue !== 'null') {
      return normalizeTimeValue(stringValue) // 递归处理转换后的字符串
    }

    // 无法处理的情况
    console.warn('Cannot normalize time value:', timeValue, typeof timeValue)
    return ''
  } catch (e) {
    console.error('Error normalizing time value:', e, timeValue)
    return '' // 出错时返回空字符串
  }
}

// --- Combined Time for Display (if still needed, though el-time-picker shows the value) ---
// These computed properties might be less critical now if the time is shown directly in the picker.
// Kept for potential display elsewhere or logging.
const combinedReservationOpenTime = computed(() => {
  try {
    const normalizedTime = normalizeTimeValue(reservationForm.value.reservationOpenTime)
    return normalizedTime ? normalizedTime.substring(0, 5) : ''
  } catch (e) {
    console.warn('combinedReservationOpenTime error:', e)
    return ''
  }
})

const combinedStartTime = computed(() => {
  try {
    const normalizedTime = normalizeTimeValue(reservationForm.value.startTime)
    return normalizedTime ? normalizedTime.substring(0, 5) : ''
  } catch (e) {
    console.warn('combinedStartTime error:', e)
    return ''
  }
})

const combinedEndTime = computed(() => {
  try {
    const normalizedTime = normalizeTimeValue(reservationForm.value.endTime)
    return normalizedTime ? normalizedTime.substring(0, 5) : ''
  } catch (e) {
    console.warn('combinedEndTime error:', e)
    return ''
  }
})

// --- Validation Rules ---
const reservationRules = reactive({
  schoolId: [{ required: true, message: '请选择学校', trigger: 'change' }],
  roomId: [{ required: true, message: '请选择房间', trigger: 'change' }],
  seatId: [
    { required: true, message: '请输入座位号', trigger: 'blur' },
    { pattern: /^[A-Za-z0-9-]+$/, message: '座位号只能包含字母、数字或连字符', trigger: 'blur' }
  ],
  reservationOpenTime: [
    { required: false, message: '请选择预约开放时间', trigger: 'change' }
  ],
  startTime: [
    { required: true, message: '请选择开始时间', trigger: 'change' }
  ],
  endTime: [
    { required: true, message: '请选择结束时间', trigger: 'change' }
  ],
  reservationType: [{ required: true, message: '请选择预约类型', trigger: 'change' }],
})

// --- Data Fetching and Processing ---
const fetchSchools = async () => {
  schoolsLoading.value = true
  try {
    const response = await api.get('/schools')
    if (response.data.code === 200) {
      schools.value = response.data.data
    } else {
      ElMessage.error('获取学校列表失败: ' + response.data.message)
    }
  } catch (error) {
    ElMessage.error('获取学校列表出错')
  } finally {
    schoolsLoading.value = false
  }
}

const onSchoolChange = async (schoolId) => {
  reservationForm.value.roomId = null
  rooms.value = []
  if (schoolId) {
    roomsLoading.value = true
    try {
      const response = await api.get(`/schools/${schoolId}/rooms`)
      if (response.data.code === 200) {
        rooms.value = response.data.data
      } else {
        ElMessage.error('获取房间列表失败: ' + response.data.message)
      }
    } catch (error) {
      ElMessage.error('获取房间列表出错')
    } finally {
      roomsLoading.value = false
    }
  }
}

const onRoomChange = (roomId) => {
  // console.log("Room changed:", roomId);
  // Potentially fetch room details or available seats info here
}

const formatTimeToHHMMSS = (dateTimeString) => {
  if (!dateTimeString) return ''
  try {
    const date = new Date(dateTimeString)
    // Check if it's a valid date object. Invalid date string can result in "Invalid Date" from new Date().
    if (isNaN(date.getTime())) { 
        // If it's already in HH:mm or HH:mm:ss format, use it directly if valid
        if (typeof dateTimeString === 'string' && (dateTimeString.match(/^\d{2}:\d{2}:\d{2}$/) || dateTimeString.match(/^\d{2}:\d{2}$/))) {
            return dateTimeString.length === 5 ? dateTimeString + ':00' : dateTimeString
        }
        // console.warn('formatTimeToHHMMSS: Received invalid date string:', dateTimeString)
        return '' // Return empty or a default if it's truly invalid
    }
    const hours = date.getHours().toString().padStart(2, '0')
    const minutes = date.getMinutes().toString().padStart(2, '0')
    const seconds = date.getSeconds().toString().padStart(2, '0')
    return `${hours}:${minutes}:${seconds}`
  } catch (e) {
    // console.error('Error formatting time:', dateTimeString, e)
    return typeof dateTimeString === 'string' ? dateTimeString : '' // Fallback for safety
  }
}

const fetchCurrentReservation = async () => {
  try {
    const profileResponse = await api.get('/user/profile')
    if (profileResponse.data.code === 200) {
      // 保存用户信息
      userProfile.value = profileResponse.data.data

      if (profileResponse.data.data.currentReservation) {
        currentReservation.value = profileResponse.data.data.currentReservation
        const reservation = profileResponse.data.data.currentReservation
        hasExistingReservation.value = true

      // Pre-fill form
      reservationForm.value.seatId = reservation.seatId
      reservationForm.value.reservationType = reservation.reservationType || 'SAME_DAY'
      
      // Ensure times are formatted as HH:mm:ss for el-time-picker
      reservationForm.value.reservationOpenTime = formatTimeToHHMMSS(reservation.reservationOpenTime)
      reservationForm.value.startTime = formatTimeToHHMMSS(reservation.startTime)
      reservationForm.value.endTime = formatTimeToHHMMSS(reservation.endTime)

      // Set school and room. This part might need adjustment based on how school/room data is structured.
      // Assuming reservation object has schoolId and roomId or enough info to derive them.
      // This example assumes you might need to fetch schools/rooms list first, then find by ID/Name.
      if (schools.value.length === 0) await fetchSchools()
      
      // If API provides school name and room name, find their IDs
      const school = schools.value.find(s => s.name === reservation.schoolName)
      if (school) {
        reservationForm.value.schoolId = school.id
        await onSchoolChange(school.id) // This will also load rooms for the school
        // Wait for rooms to load
        await nextTick() 
        const room = rooms.value.find(r => r.name === reservation.roomName)
        if (room) {
          reservationForm.value.roomId = room.id
        } else if (reservation.roomId) { // Fallback if name matching fails but roomId is present
            reservationForm.value.roomId = reservation.roomId
        }
      } else if (reservation.roomId) { // If school name not present, but roomId (which implies a school) is
          // This case is harder if you don't have schoolId directly.
          // You might need an API endpoint to get room details (including schoolId) by roomId.
          // For simplicity, this path is not fully resolved here.
          // console.warn("Could not determine school from current reservation by name.")
      }


      } else {
        hasExistingReservation.value = false
      }
    }
  } catch (error) {
    // console.error("Error fetching current reservation:", error)
    hasExistingReservation.value = false // Ensure this is reset on error
  }
}


// --- Computed Properties for UI ---
const selectedSchool = computed(() => schools.value.find(s => s.id === reservationForm.value.schoolId))
const selectedRoom = computed(() => rooms.value.find(r => r.id === reservationForm.value.roomId))

const reservationDuration = computed(() => {
  try {
    // 使用标准化的时间值
    const startTime = normalizeTimeValue(reservationForm.value.startTime)
    const endTime = normalizeTimeValue(reservationForm.value.endTime)
    
    if (startTime && endTime) {
      // 确保时间值是字符串
      if (typeof startTime !== 'string' || typeof endTime !== 'string') {
        console.warn('Time values are not strings in duration calculation:', { startTime, endTime })
        return null
      }

      const start = new Date(`2000/01/01 ${startTime}`)
      const end = new Date(`2000/01/01 ${endTime}`)
      
      if (isNaN(start.getTime()) || isNaN(end.getTime())) {
        console.warn('Invalid dates in duration calculation:', { startTime, endTime })
        return null
      }

      let diffMinutes = (end.getTime() - start.getTime()) / (1000 * 60)
      if (diffMinutes < 0) { // Handles overnight booking if that's a feature, though not typical for seat reservation
        return "时间范围无效" // More likely an error for seat booking
      }
      const hours = Math.floor(diffMinutes / 60)
      const minutes = Math.floor(diffMinutes % 60)
      if (hours === 0 && minutes === 0) return "0分钟"
      return minutes > 0 ? `${hours}小时${minutes}分钟` : `${hours}小时`
    }
    return null
  } catch (e) {
    console.error('Error calculating reservation duration:', e)
    return null // Error parsing time
  }
})


// --- Form Submission ---
const submitReservation = async () => {
  if (!reservationFormRef.value) return

  try {
    // 安全地处理表单验证
    let valid = false
    try {
      valid = await reservationFormRef.value.validate()
    } catch (validationError) {
      console.warn('表单验证过程中出现错误:', validationError)
      ElMessage.error('表单验证失败，请检查输入项')
      return
    }

    if (!valid) {
      ElMessage.error('请检查表单输入项')
      return
    }

    // 检查用户剩余天数
    if (userProfile.value && userProfile.value.remainingDays <= 0) {
      ElMessage.error('您的剩余天数不足，无法进行预约。请联系管理员充值。')
      return
    }

    // 最终时间验证检查
    const finalTimeValidation = validateTimeBeforeSubmit()
    if (!finalTimeValidation.valid) {
      ElMessage.error(finalTimeValidation.message)
      return
    }
    
    // 确认提交前显示详细的预约信息变更
    let confirmMessage = `确定要${hasExistingReservation.value ? '更新' : '提交'}此预约吗?\n\n`
    
    if (hasExistingReservation.value && currentReservation.value) {
      confirmMessage += '原预约信息:\n'
      confirmMessage += `房间: ${currentReservation.value.roomName}\n`
      confirmMessage += `座位: ${currentReservation.value.seatId}\n`
      confirmMessage += `时间: ${currentReservation.value.startTime} - ${currentReservation.value.endTime}\n\n`
      confirmMessage += '新预约信息:\n'
    }
    
    confirmMessage += `学校: ${selectedSchool.value?.name || '未选择'}\n`
    confirmMessage += `房间: ${selectedRoom.value?.name || '未选择'}\n`
    confirmMessage += `座位: ${reservationForm.value.seatId}\n`
    confirmMessage += `预约类型: ${reservationForm.value.reservationType === 'SAME_DAY' ? '当天预约' : '提前一天预约'}\n`
    confirmMessage += `时间: ${combinedStartTime.value} - ${combinedEndTime.value}`
    
    await ElMessageBox.confirm(
      confirmMessage,
      hasExistingReservation.value ? '确认更新预约' : '确认提交预约',
      { confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning' }
    )
    
    submitting.value = true

    // 使用标准化的时间值构建API参数
    const todayDateStr = new Date().toISOString().split('T')[0]
    const normalizedStartTime = normalizeTimeValue(reservationForm.value.startTime)
    const normalizedEndTime = normalizeTimeValue(reservationForm.value.endTime)
    const payloadStartTime = `${todayDateStr}T${normalizedStartTime}`
    const payloadEndTime = `${todayDateStr}T${normalizedEndTime}`
    const normalizedReservationOpenTime = normalizeTimeValue(reservationForm.value.reservationOpenTime)

    const params = {
      roomId: reservationForm.value.roomId,
      seatId: reservationForm.value.seatId,
      reservationOpenTime: normalizedReservationOpenTime || null,
      reservationType: reservationForm.value.reservationType,
      startTime: payloadStartTime,
      endTime: payloadEndTime,
    }
    
    console.log('提交预约参数:', params)
    console.log('是否为更新操作:', hasExistingReservation.value)
    
    const response = await api.post('/reservations/create', null, { params })

    if (response.data.code === 200) {
      const isUpdate = hasExistingReservation.value
      ElMessage.success(response.data.message || (isUpdate ? '预约更新成功!' : '预约创建成功!'))
      
      // 更新状态
      hasExistingReservation.value = true
      currentReservation.value = response.data.data
      
      // 重新获取用户当前预约信息以确保数据同步
      await fetchCurrentReservation()
      
      console.log(`预约${isUpdate ? '更新' : '创建'}成功，返回数据:`, response.data.data)
    } else {
      ElMessage.error(response.data.message || '预约失败')
    }
  } catch (error) {
    if (error !== 'cancel') { // User cancelled ElMessageBox
      console.error('预约提交失败:', error)
      
      // 检查是否是座位冲突错误
      const errorMsg = error.response?.data?.message || '预约操作失败，请稍后再试'
      
      if (errorMsg.includes('Duplicate entry') && errorMsg.includes('unique_active_seat_reservation')) {
        // 座位已被其他用户占用
        ElMessage.error(`座位 ${reservationForm.value.seatId} 已被其他用户预约，请选择其他座位`)
      } else if (errorMsg.includes('该座位已被其他用户预约')) {
        // 后端返回的中文错误信息
        ElMessage.error(`座位 ${reservationForm.value.seatId} 已被占用，请选择其他座位`)
      } else {
        // 其他错误
        ElMessage.error(errorMsg)
      }
    }
  } finally {
    submitting.value = false
  }
}

// 提交前的最终时间验证
const validateTimeBeforeSubmit = () => {
  try {
    const start = normalizeTimeValue(reservationForm.value.startTime)
    const end = normalizeTimeValue(reservationForm.value.endTime)
    
    if (!start || !end) {
      return { valid: false, message: '请选择完整的开始时间和结束时间' }
    }
    
    const timeRegex = /^\d{2}:\d{2}:\d{2}$/
    if (!timeRegex.test(start) || !timeRegex.test(end)) {
      return { valid: false, message: '时间格式不正确，请重新选择' }
    }
    
    const startDate = new Date(`2000/01/01 ${start}`)
    const endDate = new Date(`2000/01/01 ${end}`)
    
    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
      return { valid: false, message: '时间格式无效，请重新选择' }
    }
    
    if (startDate >= endDate) {
      return { valid: false, message: '结束时间必须在开始时间之后，请调整时间选择' }
    }
    
    const diffMinutes = (endDate.getTime() - startDate.getTime()) / (1000 * 60)
    if (diffMinutes < 15) {
      return { valid: false, message: '预约时间间隔至少需要15分钟，请调整时间选择' }
    }
    
    return { valid: true, message: '时间验证通过' }
    
  } catch (error) {
    console.error('Final time validation error:', error)
    return { valid: false, message: '时间验证出现问题，请检查时间选择' }
  }
}

const refreshTimeDisplay = () => {
  // With el-time-picker, values are directly bound.
  // This function can be used to manually re-trigger validation for the whole form if needed.
  // reservationFormRef.value?.validate((valid) => {
  //   if (valid) {
  //     ElMessage.success('表单校验通过!')
  //   } else {
  //     ElMessage.error('表单存在错误项!')
  //   }
  // })
  ElMessage.info('时间选择器已是最新状态。')
}

const goBack = () => {
  router.push('/dashboard')
}

// 根据剩余天数返回标签类型
const getRemainingDaysType = (days) => {
  const actualDays = days !== undefined ? days : 0
  if (actualDays <= 0) return 'danger'
  if (actualDays === 1) return 'warning'
  if (actualDays <= 3) return 'success'
  return 'primary'
}

// --- 全局错误处理 ---
const handleGlobalError = (error, context = '未知操作') => {
  console.error(`${context}过程中出现错误:`, error)

  // 避免重复显示错误消息
  if (!error.handled) {
    error.handled = true
    ElMessage.error(`${context}失败，请稍后重试`)
  }
}

// 添加全局未处理Promise错误监听
if (typeof window !== 'undefined') {
  window.addEventListener('unhandledrejection', (event) => {
    console.error('未处理的Promise错误:', event.reason)
    handleGlobalError(event.reason, '异步操作')
    event.preventDefault() // 阻止错误在控制台显示
  })
}

// --- Lifecycle Hooks ---
onMounted(async () => {
  try {
    await fetchSchools() // Fetch schools first
    await fetchCurrentReservation() // Then fetch current reservation which might depend on school/room lists
  } catch (error) {
    handleGlobalError(error, '页面初始化')
  }
})


// --- Watchers (optional, as v-model and @change should handle most things) ---
// Example: if you need to react to specific time changes for complex logic not covered by validators.
// watch(() => reservationForm.value.startTime, (newTime, oldTime) => {
//   if (newTime) {
//     // console.log('Watcher: startTime changed to', newTime)
//     // reservationFormRef.value?.validateField('endTime') // Re-validate end time
//   }
// })

// watch(() => reservationForm.value.endTime, (newTime, oldTime) => {
//   if (newTime) {
//     // console.log('Watcher: endTime changed to', newTime)
//     // reservationFormRef.value?.validateField('startTime') // Re-validate start time
//   }
// })

</script>

<style scoped>
.reservation-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.reservation-header {
  background: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  padding: 0 20px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-left h2 {
  color: #333;
  margin: 0;
}

.reservation-main {
  padding: 20px;
}

.reservation-content {
  max-width: 1200px;
  margin: 0 auto;
}

.form-card, .info-card {
  height: fit-content;
}

.reservation-form {
  padding: 20px 0;
}

.info-section {
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #ebeef5;
}

.info-section:last-child {
  border-bottom: none;
}

.info-section h4 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.info-section p {
  margin: 4px 0;
  color: #606266;
  font-size: 14px;
}

.rules-list {
  margin: 0;
  padding-left: 20px;
  color: #909399;
  font-size: 13px;
}

.rules-list li {
  margin-bottom: 4px;
  line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .reservation-header {
    padding: 0 10px;
  }
  
  .reservation-main {
    padding: 10px;
  }
  
  .header-left h2 {
    font-size: 18px;
  }
}
</style> 