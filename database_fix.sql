-- 修复数据库字段缺失问题
USE seat_reservation;

-- 添加缺失的字段到reservations表
ALTER TABLE reservations ADD COLUMN last_execution_time TIMESTAMP NULL COMMENT '最后执行时间';
ALTER TABLE reservations ADD COLUMN execution_result TEXT COMMENT '执行结果(JSON)';

-- 扩展status枚举，添加自动预约状态
ALTER TABLE reservations MODIFY status ENUM('ACTIVE', 'CANCELLED', 'PAUSED', 'AUTO_PENDING', 'AUTO_SUCCESS', 'AUTO_FAILED') NOT NULL DEFAULT 'ACTIVE';

-- 创建执行日志表
CREATE TABLE IF NOT EXISTS auto_execution_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    reservation_id BIGINT NOT NULL COMMENT '预约记录ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    execution_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '执行时间',
    execution_status ENUM('SUCCESS', 'FAILED', 'TIMEOUT') NOT NULL,
    result_message TEXT COMMENT '执行结果信息',
    execution_duration INT COMMENT '执行耗时(秒)',
    
    INDEX idx_reservation_time (reservation_id, execution_time),
    INDEX idx_user_status (user_id, execution_status),
    FOREIGN KEY (reservation_id) REFERENCES reservations(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) COMMENT '自动预约执行日志表';

-- 验证更改
DESCRIBE reservations;
DESCRIBE auto_execution_logs;

-- 显示完成信息
SELECT 'Database update completed successfully!' as result;
