import json
import glob
import re
from tabulate import tabulate # 导入 tabulate 库

def extract_data():
    # 精确匹配config+数字.json文件
    config_files = sorted(
        [f for f in glob.glob('config*.json') 
         if re.match(r'config\d+\.json$', f)],
        key=lambda x: int(re.search(r'\d+', x).group())
    )
    
    total_files = 0
    total_records = 0
    grouped_records = {}
    duplicate_count = 0
    
    # 收集所有记录
    for file in config_files:
        try:
            with open(file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                
            records = data.get('reserve', [])
            if not records:
                continue
                
            total_files += 1
            total_records += len(records)
            
            for reserve in records:
                comment = reserve.get('_comment', '')
                roomid = reserve.get('roomid', '')
                seatid = ','.join(reserve.get('seatid', []))
                
                record_key = f"{roomid}|{seatid}"
                record_value = f"{comment} | {roomid} | {seatid}|{file}"
                
                if record_key not in grouped_records:
                    if roomid not in grouped_records:
                        grouped_records[roomid] = {}
                    grouped_records[roomid][record_key] = record_value
                else:
                    duplicate_count += 1
                    
        except Exception as e:
            print(f"处理文件 {file} 时出错: {str(e)}")
    
    # 按roomid排序并分组输出表格
    output_content = [] # 使用列表存储最终输出的各部分字符串
    output_content.append("=== 分组结果 ===")
    headers = ["使用者", "座位ID", "来源文件"] # 表头

    first_table = True # 标记是否是第一个表格，用于控制前导换行
    for roomid in sorted(grouped_records.keys()):
        if not first_table:
            output_content.append("\n") # 在非首个表格前加空行分隔
        else:
            first_table = False
            
        output_content.append(f"--- 房间ID: {roomid} ---") # 分组标题
        table_data = []
        for record_value in grouped_records[roomid].values():
            # 解析 record_value: "comment | roomid | seatid|file"
            parts = record_value.split('|')
            if len(parts) == 4: # 确保解析正确
                comment = parts[0].strip()
                seat_id_val = parts[2].strip()
                file_val = parts[3].strip()
                table_data.append([comment, seat_id_val, file_val])
            else:
                print(f"警告：无法解析记录 '{record_value}' (房间ID: {roomid})，跳过。")

        if table_data: # 只有当该房间有数据时才生成表格
            table_output = tabulate(table_data, headers=headers, tablefmt="grid")
            output_content.append(table_output)
        else:
            output_content.append("  (无有效记录)")

    # 统计信息
    output_content.append("\n\n=== 统计 ===") # 增加换行以分隔
    output_content.append(f"处理文件总数: {total_files} 个")
    output_content.append(f"总记录数: {total_records} 条")
    output_content.append(f"有效记录数: {sum(len(v) for v in grouped_records.values())} 条 (已去重)")
    output_content.append(f"重复记录数: {duplicate_count} 条")

    # 最终输出内容
    final_output = '\n'.join(output_content)

    # 输出到控制台和文件
    print(final_output)
    with open('汇总房间.txt', 'w', encoding='utf-8') as f:
        f.write(final_output)

if __name__ == '__main__':
    extract_data()
