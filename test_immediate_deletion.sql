-- 测试立即删除功能的SQL脚本
USE seat_reservation;

-- 1. 查看test13用户的当前状态
SELECT 
    u.id,
    u.username,
    u.name,
    u.remaining_days,
    u.role,
    u.created_time
FROM users u 
WHERE u.username = 'test13' OR u.id = 13;

-- 2. 查看test13用户的所有预约记录
SELECT 
    r.id as reservation_id,
    r.user_id,
    r.room_id,
    r.seat_id,
    r.start_time,
    r.end_time,
    r.status,
    r.created_time,
    r.reservation_open_time,
    r.reservation_type
FROM reservations r
WHERE r.user_id = 13
ORDER BY r.created_time DESC;

-- 3. 查看所有剩余天数为0的用户及其预约
SELECT 
    u.id as user_id,
    u.username,
    u.remaining_days,
    r.id as reservation_id,
    r.seat_id,
    r.status,
    r.start_time,
    r.end_time
FROM users u
LEFT JOIN reservations r ON u.id = r.user_id AND r.status = 'ACTIVE'
WHERE u.remaining_days <= 0 AND u.role != 'ADMIN'
ORDER BY u.id;

-- 4. 手动执行删除逻辑（模拟后端代码）
-- 这会删除所有剩余天数为0的用户的活跃预约
DELETE r FROM reservations r
INNER JOIN users u ON r.user_id = u.id
WHERE u.remaining_days <= 0 
AND r.status = 'ACTIVE'
AND u.role != 'ADMIN';

-- 5. 显示删除了多少条记录
SELECT ROW_COUNT() as deleted_count;

-- 6. 再次查看test13用户的预约记录（应该没有ACTIVE状态的了）
SELECT 
    r.id as reservation_id,
    r.user_id,
    r.room_id,
    r.seat_id,
    r.start_time,
    r.end_time,
    r.status,
    r.created_time
FROM reservations r
WHERE r.user_id = 13
ORDER BY r.created_time DESC;

-- 7. 验证所有剩余天数为0的用户是否还有活跃预约
SELECT 
    u.id as user_id,
    u.username,
    u.remaining_days,
    COUNT(r.id) as active_reservations_count
FROM users u
LEFT JOIN reservations r ON u.id = r.user_id AND r.status = 'ACTIVE'
WHERE u.remaining_days <= 0 AND u.role != 'ADMIN'
GROUP BY u.id, u.username, u.remaining_days
HAVING COUNT(r.id) > 0;

-- 8. 如果上面的查询返回任何结果，说明还有剩余天数为0但仍有活跃预约的用户

SELECT 'Test completed. Check results above.' as message;
