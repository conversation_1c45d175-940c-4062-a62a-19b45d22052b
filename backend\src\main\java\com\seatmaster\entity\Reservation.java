package com.seatmaster.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * 预约实体类
 * 对应数据库表：reservations
 */
@Data
@TableName("reservations")
public class Reservation {
    @TableId(type = IdType.AUTO)
    private Long id;
    
    private Long userId;
    
    private Long roomId;
    
    private String seatId; // 座位号，用户自选
    
    private LocalTime startTime; // 预约开始时间（仅时间，格式HH:MM:SS）
    
    private LocalTime endTime; // 预约结束时间（仅时间，格式HH:MM:SS）
    
    /**
     * 预约状态：ACTIVE-活跃, CANCELLED-已取消, PAUSED-暂停,
     * AUTO_PENDING-等待自动执行, AUTO_SUCCESS-自动预约成功, AUTO_FAILED-自动预约失败
     */
    private String status;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdTime;

    /**
     * 预约开放时间设置，用户可选填写 (格式: HH:mm:ss)
     * 系统将在每天的这个时间点执行自动预约
     */
    private String reservationOpenTime;

    /**
     * 预约类型：SAME_DAY当天预约，ADVANCE_ONE_DAY提前一天预约
     */
    private String reservationType;

    /**
     * 最后执行时间 (新增字段)
     * 记录自动预约最后一次执行的时间
     */
    private LocalDateTime lastExecutionTime;

    /**
     * 执行结果 (新增字段)
     * 存储自动预约执行结果的JSON字符串
     */
    private String executionResult;

    /**
     * 预约状态枚举
     */
    public enum ReservationStatus {
        ACTIVE("ACTIVE", "活跃"),
        CANCELLED("CANCELLED", "已取消"),
        PAUSED("PAUSED", "暂停"),
        AUTO_PENDING("AUTO_PENDING", "等待自动执行"),
        AUTO_SUCCESS("AUTO_SUCCESS", "自动预约成功"),
        AUTO_FAILED("AUTO_FAILED", "自动预约失败");

        private final String code;
        private final String description;

        ReservationStatus(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 预约类型枚举
     */
    public enum ReservationType {
        SAME_DAY("SAME_DAY", "当天预约"),
        ADVANCE_ONE_DAY("ADVANCE_ONE_DAY", "提前一天预约");

        private final String code;
        private final String description;

        ReservationType(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }
}