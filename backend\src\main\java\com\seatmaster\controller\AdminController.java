package com.seatmaster.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.seatmaster.common.Result;
import com.seatmaster.dto.UserProfileResponse;
import com.seatmaster.entity.Reservation;
import com.seatmaster.entity.Room;
import com.seatmaster.entity.User;
import com.seatmaster.mapper.ReservationMapper;
import com.seatmaster.mapper.RoomMapper;
import com.seatmaster.mapper.UserMapper;
import com.seatmaster.service.ReservationService;
import com.seatmaster.service.impl.ReservationServiceImpl;
import com.seatmaster.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/api/admin")
@CrossOrigin(origins = "*")
@PreAuthorize("hasRole('ADMIN')")
public class AdminController {
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private UserMapper userMapper;
    
    @Autowired
    private ReservationMapper reservationMapper;
    
    @Autowired
    private RoomMapper roomMapper;
    
    @Autowired
    private ReservationService reservationService;

    @Autowired
    private ReservationServiceImpl reservationServiceImpl;
    
    /**
     * 获取管理员统计数据
     */
    @GetMapping("/stats")
    public Result<?> getAdminStats() {
        try {
            Map<String, Object> stats = new HashMap<>();
            
            // 总用户数（排除管理员）
            QueryWrapper<User> userWrapper = new QueryWrapper<>();
            userWrapper.ne("role", "ADMIN");
            long totalUsers = userMapper.selectCount(userWrapper);
            stats.put("totalUsers", totalUsers);
            
            // 活跃预约数
            QueryWrapper<Reservation> activeWrapper = new QueryWrapper<>();
            activeWrapper.eq("status", "ACTIVE");
            long activeReservations = reservationMapper.selectCount(activeWrapper);
            stats.put("activeReservations", activeReservations);
            
            // 总房间数
            long totalRooms = roomMapper.selectCount(null);
            stats.put("totalRooms", totalRooms);
            
            // 即将到期的用户（剩余天数<=1）
            QueryWrapper<User> expiringWrapper = new QueryWrapper<>();
            expiringWrapper.ne("role", "ADMIN").le("remaining_days", 1);
            long expiringSoon = userMapper.selectCount(expiringWrapper);
            stats.put("expiringSoon", expiringSoon);
            
            return Result.success("获取统计数据成功", stats);
            
        } catch (Exception e) {
            log.error("获取管理员统计数据失败", e);
            return Result.error("获取统计数据失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取所有用户信息（包含预约信息）
     */
    @GetMapping("/users")
    public Result<?> getAllUsers() {
        try {
            // 获取所有用户（排除管理员）
            QueryWrapper<User> userWrapper = new QueryWrapper<>();
            userWrapper.ne("role", "ADMIN").orderByDesc("created_time");
            List<User> users = userMapper.selectList(userWrapper);
            
            // 为每个用户获取预约信息
            List<UserProfileResponse> userProfiles = users.stream().map(user -> {
                UserProfileResponse profile = new UserProfileResponse();
                profile.setId(user.getId());
                profile.setUsername(user.getUsername());
                profile.setName(user.getName());
                profile.setRole(user.getRole().toString());
                profile.setCreatedTime(user.getCreatedTime());
                profile.setRemainingDays(user.getRemainingDays()); // 添加剩余天数
                
                // 获取当前预约信息
                UserProfileResponse.CurrentReservation currentReservation = 
                    reservationMapper.getCurrentReservationByUserId(user.getId());
                profile.setCurrentReservation(currentReservation);
                
                return profile;
            }).collect(Collectors.toList());
            
            return Result.success("获取用户列表成功", userProfiles);
            
        } catch (Exception e) {
            log.error("获取用户列表失败", e);
            return Result.error("获取用户列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 修改用户的剩余天数
     */
    @PutMapping("/users/{userId}/remaining-days")
    public Result<?> updateRemainingDays(@PathVariable Long userId, 
                                       @RequestBody Map<String, Object> request) {
        try {
            Integer remainingDays = (Integer) request.get("remainingDays");
            
            if (remainingDays == null || remainingDays < 0 || remainingDays > 365) {
                return Result.error("剩余天数必须在0-365之间");
            }
            
            // 查找用户记录
            User user = userMapper.selectById(userId);
            if (user == null) {
                return Result.error("用户不存在");
            }
            
            // 更新剩余天数
            user.setRemainingDays(remainingDays);
            int updateResult = userMapper.updateById(user);
            
            if (updateResult > 0) {
                log.info("管理员修改用户{}的剩余天数为{}", userId, remainingDays);

                // 立即检查并删除剩余天数为0的用户预约
                if (remainingDays <= 0) {
                    log.info("用户{}剩余天数设为{}，立即删除现有预约且不允许新预约", userId, remainingDays);
                    reservationServiceImpl.checkAndDeleteUserReservationsIfZeroDays(userId);
                } else {
                    log.info("用户{}剩余天数设为{}，状态正常", userId, remainingDays);
                }

                return Result.success("修改成功", null);
            } else {
                return Result.error("修改失败");
            }
            
        } catch (Exception e) {
            log.error("修改剩余天数失败", e);
            return Result.error("修改失败: " + e.getMessage());
        }
    }
    
    /**
     * 管理员取消用户预约
     */
    @PostMapping("/reservations/cancel/{reservationId}")
    public Result<?> cancelUserReservation(@PathVariable Long reservationId) {
        try {
            // 查找预约记录
            Reservation reservation = reservationMapper.selectById(reservationId);
            if (reservation == null) {
                return Result.error("预约记录不存在");
            }
            
            if (!"ACTIVE".equals(reservation.getStatus())) {
                return Result.error("预约已经被取消");
            }
            
            // 取消预约
            reservation.setStatus("CANCELLED");
            int updateResult = reservationMapper.updateById(reservation);
            
            if (updateResult > 0) {
                log.info("管理员取消了预约{}", reservationId);
                return Result.success("预约已取消", null);
            } else {
                return Result.error("取消预约失败");
            }
            
        } catch (Exception e) {
            log.error("取消预约失败", e);
            return Result.error("取消预约失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取指定用户的详细信息
     */
    @GetMapping("/users/{userId}")
    public Result<?> getUserDetail(@PathVariable Long userId) {
        try {
            User user = userService.findById(userId);
            if (user == null) {
                return Result.error("用户不存在");
            }
            
            UserProfileResponse profile = new UserProfileResponse();
            profile.setId(user.getId());
            profile.setUsername(user.getUsername());
            profile.setName(user.getName());
            profile.setRole(user.getRole().toString());
            profile.setCreatedTime(user.getCreatedTime());
            profile.setRemainingDays(user.getRemainingDays()); // 添加剩余天数
            
            // 获取当前预约信息
            UserProfileResponse.CurrentReservation currentReservation = 
                reservationMapper.getCurrentReservationByUserId(user.getId());
            profile.setCurrentReservation(currentReservation);
            
            // 获取所有预约记录（调试用）
            List<UserProfileResponse.CurrentReservation> allReservations = 
                reservationMapper.getAllReservationsByUserId(user.getId());
            
            Map<String, Object> result = new HashMap<>();
            result.put("user", profile);
            result.put("allReservations", allReservations);
            
            return Result.success("获取用户详情成功", result);

        } catch (Exception e) {
            log.error("获取用户详情失败", e);
            return Result.error("获取用户详情失败: " + e.getMessage());
        }
    }

    /**
     * 手动触发删除剩余天数为0的用户预约（临时调试用）
     */
    @PostMapping("/manage-reservation-status")
    public Result<?> manageReservationStatus() {
        try {
            // 删除剩余天数为0的用户的预约
            reservationServiceImpl.deleteReservationsForUsersWithZeroDays();

            return Result.success("预约状态管理完成", null);

        } catch (Exception e) {
            log.error("预约状态管理失败", e);
            return Result.error("预约状态管理失败: " + e.getMessage());
        }
    }
}