package com.seatmaster.dto;

import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 自动预约执行结果DTO
 * 用于封装预约执行的结果信息
 */
@Data
@Builder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
public class ExecutionResult {
    
    /**
     * 执行是否成功
     */
    private boolean success;
    
    /**
     * 执行结果消息
     */
    private String message;
    
    /**
     * 详细信息
     */
    private String details;
    
    /**
     * 执行时间戳
     */
    private LocalDateTime timestamp;
    
    /**
     * 执行耗时(毫秒)
     */
    private Long duration;
    
    /**
     * 错误代码(可选)
     */
    private String errorCode;
    
    /**
     * 创建成功结果
     * 
     * @param message 成功消息
     * @return ExecutionResult
     */
    public static ExecutionResult success(String message) {
        return ExecutionResult.builder()
            .success(true)
            .message(message)
            .timestamp(LocalDateTime.now())
            .build();
    }
    
    /**
     * 创建成功结果(带详细信息)
     * 
     * @param message 成功消息
     * @param details 详细信息
     * @return ExecutionResult
     */
    public static ExecutionResult success(String message, String details) {
        return ExecutionResult.builder()
            .success(true)
            .message(message)
            .details(details)
            .timestamp(LocalDateTime.now())
            .build();
    }
    
    /**
     * 创建失败结果
     * 
     * @param message 失败消息
     * @return ExecutionResult
     */
    public static ExecutionResult failure(String message) {
        return ExecutionResult.builder()
            .success(false)
            .message(message)
            .timestamp(LocalDateTime.now())
            .build();
    }
    
    /**
     * 创建失败结果(带错误代码)
     * 
     * @param message 失败消息
     * @param errorCode 错误代码
     * @return ExecutionResult
     */
    public static ExecutionResult failure(String message, String errorCode) {
        return ExecutionResult.builder()
            .success(false)
            .message(message)
            .errorCode(errorCode)
            .timestamp(LocalDateTime.now())
            .build();
    }
    
    /**
     * 创建失败结果(带详细信息和错误代码)
     * 
     * @param message 失败消息
     * @param details 详细信息
     * @param errorCode 错误代码
     * @return ExecutionResult
     */
    public static ExecutionResult failure(String message, String details, String errorCode) {
        return ExecutionResult.builder()
            .success(false)
            .message(message)
            .details(details)
            .errorCode(errorCode)
            .timestamp(LocalDateTime.now())
            .build();
    }
    
    /**
     * 设置执行耗时
     * 
     * @param duration 耗时(毫秒)
     * @return ExecutionResult
     */
    public ExecutionResult withDuration(Long duration) {
        this.duration = duration;
        return this;
    }
    
    /**
     * 设置详细信息
     * 
     * @param details 详细信息
     * @return ExecutionResult
     */
    public ExecutionResult withDetails(String details) {
        this.details = details;
        return this;
    }
}
