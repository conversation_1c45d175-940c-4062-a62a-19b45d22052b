package com.seatmaster.controller;

import com.seatmaster.common.Result;
import com.seatmaster.dto.ChangePasswordRequest;
import com.seatmaster.dto.UpdateUserRequest;
import com.seatmaster.dto.UserProfileResponse;
import com.seatmaster.entity.User;
import com.seatmaster.mapper.ReservationMapper;
import com.seatmaster.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@Slf4j
@RestController
@RequestMapping("/api/user")
@CrossOrigin(origins = "*")
public class UserController {
    
    @Autowired
    private UserService userService;

    @Autowired
    private ReservationMapper reservationMapper;

    @Autowired
    private com.seatmaster.service.impl.ReservationServiceImpl reservationService;
    
    /**
     * 获取当前用户信息
     */
    @GetMapping("/profile")
    public Result<?> getUserProfile(Authentication authentication) {
        try {
            String username = authentication.getName();
            log.info("获取用户信息请求，用户名: {}", username);

            User user = userService.findByUsername(username);

            if (user == null) {
                log.warn("用户不存在: {}", username);
                return Result.error("用户不存在");
            }

            log.info("找到用户: {}, ID: {}, 剩余天数: {}", user.getUsername(), user.getId(), user.getRemainingDays());

            // 立即检查并删除剩余天数为0的用户预约
            log.info("开始检查用户{}的剩余天数，当前剩余天数: {}", user.getId(), user.getRemainingDays());
            if (user.getRemainingDays() <= 0) {
                log.warn("用户{}剩余天数为{}，将立即删除其预约记录", user.getId(), user.getRemainingDays());
            }
            reservationService.checkAndDeleteUserReservationsIfZeroDays(user.getId());
            log.info("完成检查用户{}的预约删除操作", user.getId());
            
            // 创建响应对象
            UserProfileResponse response = new UserProfileResponse();
            response.setId(user.getId());
            response.setUsername(user.getUsername());
            response.setName(user.getName());
            response.setRole(user.getRole().toString());
            response.setCreatedTime(user.getCreatedTime());
            response.setRemainingDays(user.getRemainingDays());

            // 设置用户状态
            if (user.getRemainingDays() <= 0) {
                response.setUserStatus("SUSPENDED"); // 暂停
            } else {
                response.setUserStatus("ACTIVE"); // 正常
            }
            
            // 获取当前预约信息 - 多种查询策略
            log.info("正在查询用户ID {}的预约信息", user.getId());
            
            // 1. 先测试用户是否有任何预约
            int reservationCount = reservationMapper.countReservationsByUserId(user.getId());
            log.info("用户ID {}的预约总数: {}", user.getId(), reservationCount);
            
            // 2. 获取所有预约记录（调试用）
            java.util.List<UserProfileResponse.CurrentReservation> allReservations = 
                reservationMapper.getAllReservationsByUserId(user.getId());
            log.info("用户ID {}的所有预约记录数: {}", user.getId(), allReservations.size());
            for (UserProfileResponse.CurrentReservation res : allReservations) {
                log.info("预约记录: ID={}, 学校={}, 房间={}, 座位={}, 状态={}, 开始时间={}, 结束时间={}", 
                    res.getReservationId(), res.getSchoolName(), res.getRoomName(), 
                    res.getSeatId(), res.getStatus(), res.getStartTime(), res.getEndTime());
            }
            
            UserProfileResponse.CurrentReservation currentReservation = null;
            
            // 获取最新的活动预约（已去掉时间限制）
            currentReservation = reservationMapper.getCurrentReservationByUserId(user.getId());
            if (currentReservation != null) {
                log.info("找到最新预约: 学校={}, 房间={}, 座位={}",
                    currentReservation.getSchoolName(), currentReservation.getRoomName(),
                    currentReservation.getSeatId());
            } else {
                log.warn("用户ID {}没有找到任何活动预约", user.getId());
            }
            
            response.setCurrentReservation(currentReservation);
            
            return Result.success("获取用户信息成功", response);
            
        } catch (Exception e) {
            log.error("获取用户信息失败", e);
            return Result.error("获取用户信息失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新用户信息
     */
    @PutMapping("/profile")
    public Result<?> updateUserProfile(
            @Valid @RequestBody UpdateUserRequest request,
            Authentication authentication) {
        try {
            String username = authentication.getName();
            User user = userService.findByUsername(username);
            
            if (user == null) {
                return Result.error("用户不存在");
            }
            
            // 更新用户信息
            user.setName(request.getName());
            boolean updated = userService.updateById(user);
            
            if (updated) {
                // 不返回密码
                user.setPassword(null);
                return Result.success("用户信息更新成功", user);
            } else {
                return Result.error("更新失败");
            }
            
        } catch (Exception e) {
            log.error("更新用户信息失败", e);
            return Result.error("更新用户信息失败");
        }
    }
    
    /**
     * 修改密码
     */
    @PutMapping("/password")
    public Result<?> changePassword(
            @Valid @RequestBody ChangePasswordRequest request,
            Authentication authentication) {
        try {
            String username = authentication.getName();
            User user = userService.findByUsername(username);
            
            if (user == null) {
                return Result.error("用户不存在");
            }
            
            // 直接更新密码，不验证当前密码
            boolean updated = userService.changePassword(user.getId(), request.getNewPassword());
            
            if (updated) {
                return Result.success("密码修改成功", null);
            } else {
                return Result.error("密码修改失败");
            }
            
        } catch (Exception e) {
            log.error("修改密码失败", e);
            return Result.error("修改密码失败");
        }
    }
    
    /**
     * 调试端点：获取指定用户的预约信息（无需认证）
     */
    @GetMapping("/debug-profile/{userId}")
    public Result<?> getDebugUserProfile(@PathVariable Long userId) {
        try {
            log.info("调试获取用户ID {}的信息", userId);
            
            User user = userService.findById(userId);
            
            if (user == null) {
                log.warn("用户不存在: {}", userId);
                return Result.error("用户不存在");
            }
            
            log.info("找到用户: {}, ID: {}", user.getUsername(), user.getId());
            
            // 创建响应对象
            UserProfileResponse response = new UserProfileResponse();
            response.setId(user.getId());
            response.setUsername(user.getUsername());
            response.setName(user.getName());
            response.setRole(user.getRole().toString());
            response.setCreatedTime(user.getCreatedTime());
            response.setRemainingDays(user.getRemainingDays());

            // 设置用户状态
            if (user.getRemainingDays() <= 0) {
                response.setUserStatus("SUSPENDED"); // 暂停
            } else {
                response.setUserStatus("ACTIVE"); // 正常
            }
            
            // 获取当前预约信息 - 多种查询策略
            log.info("正在查询用户ID {}的预约信息", user.getId());
            
            // 1. 先测试用户是否有任何预约
            int reservationCount = reservationMapper.countReservationsByUserId(user.getId());
            log.info("用户ID {}的预约总数: {}", user.getId(), reservationCount);
            
            // 2. 获取所有预约记录（调试用）
            java.util.List<UserProfileResponse.CurrentReservation> allReservations = 
                reservationMapper.getAllReservationsByUserId(user.getId());
            log.info("用户ID {}的所有预约记录数: {}", user.getId(), allReservations.size());
            for (UserProfileResponse.CurrentReservation res : allReservations) {
                log.info("预约记录: ID={}, 学校={}, 房间={}, 座位={}, 状态={}, 开始时间={}, 结束时间={}", 
                    res.getReservationId(), res.getSchoolName(), res.getRoomName(), 
                    res.getSeatId(), res.getStatus(), res.getStartTime(), res.getEndTime());
            }
            
            UserProfileResponse.CurrentReservation currentReservation = null;
            
            // 获取最新的活动预约（已去掉时间限制）
            currentReservation = reservationMapper.getCurrentReservationByUserId(user.getId());
            if (currentReservation != null) {
                log.info("找到最新预约: 学校={}, 房间={}, 座位={}", 
                    currentReservation.getSchoolName(), currentReservation.getRoomName(), 
                    currentReservation.getSeatId());
            } else {
                log.warn("用户ID {}没有找到任何活动预约", user.getId());
            }
            
            response.setCurrentReservation(currentReservation);
            
            // 添加调试信息
            java.util.Map<String, Object> debugInfo = new java.util.HashMap<>();
            debugInfo.put("reservationCount", reservationCount);
            debugInfo.put("allReservations", allReservations);
            debugInfo.put("currentReservation", currentReservation);
            
            return Result.success("获取用户信息成功", java.util.Map.of(
                "user", response,
                "debug", debugInfo
            ));

        } catch (Exception e) {
            log.error("获取用户信息失败", e);
            return Result.error("获取用户信息失败: " + e.getMessage());
        }
    }

    /**
     * 测试端点：立即检查并删除指定用户的零天预约
     */
    @PostMapping("/test-delete-zero-days/{userId}")
    public Result<?> testDeleteZeroDaysReservations(@PathVariable Long userId) {
        try {
            log.info("测试删除用户{}的零天预约", userId);

            // 获取用户信息
            User user = userService.findById(userId);
            if (user == null) {
                return Result.error("用户不存在");
            }

            log.info("用户{}当前剩余天数: {}", userId, user.getRemainingDays());

            // 查询删除前的预约数量
            com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<com.seatmaster.entity.Reservation> beforeWrapper =
                new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<>();
            beforeWrapper.eq("user_id", userId).eq("status", "ACTIVE");
            long beforeCount = reservationMapper.selectCount(beforeWrapper);
            log.info("删除前用户{}有{}个活跃预约", userId, beforeCount);

            // 执行删除操作
            reservationService.checkAndDeleteUserReservationsIfZeroDays(userId);

            // 查询删除后的预约数量
            long afterCount = reservationMapper.selectCount(beforeWrapper);
            log.info("删除后用户{}有{}个活跃预约", userId, afterCount);

            java.util.Map<String, Object> result = new java.util.HashMap<>();
            result.put("userId", userId);
            result.put("username", user.getUsername());
            result.put("remainingDays", user.getRemainingDays());
            result.put("beforeCount", beforeCount);
            result.put("afterCount", afterCount);
            result.put("deletedCount", beforeCount - afterCount);

            return Result.success("测试完成", result);

        } catch (Exception e) {
            log.error("测试删除零天预约失败", e);
            return Result.error("测试失败: " + e.getMessage());
        }
    }
}