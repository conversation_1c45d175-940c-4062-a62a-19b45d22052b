-- 立即删除剩余天数为0的用户预约记录
-- 这个脚本会立即执行删除操作，解决当前存在的问题

USE seat_reservation;

-- 1. 查看当前剩余天数为0的用户及其预约情况
SELECT 
    u.id as user_id,
    u.username,
    u.name,
    u.remaining_days,
    r.id as reservation_id,
    r.seat_id,
    r.status,
    r.start_time,
    r.end_time,
    r.created_time
FROM users u
LEFT JOIN reservations r ON u.id = r.user_id AND r.status = 'ACTIVE'
WHERE u.remaining_days <= 0 AND u.role != 'ADMIN'
ORDER BY u.id;

-- 2. 显示即将删除的预约记录数量
SELECT 
    COUNT(*) as will_be_deleted_count,
    'ACTIVE reservations for users with 0 remaining days' as description
FROM reservations r
INNER JOIN users u ON r.user_id = u.id
WHERE u.remaining_days <= 0 
AND r.status = 'ACTIVE'
AND u.role != 'ADMIN';

-- 3. 立即删除剩余天数为0的用户的活跃预约记录
DELETE r FROM reservations r
INNER JOIN users u ON r.user_id = u.id
WHERE u.remaining_days <= 0 
AND r.status = 'ACTIVE'
AND u.role != 'ADMIN';

-- 4. 显示删除结果
SELECT ROW_COUNT() as deleted_reservations_count;

-- 5. 验证删除结果 - 查看剩余天数为0的用户是否还有活跃预约
SELECT 
    u.id as user_id,
    u.username,
    u.name,
    u.remaining_days,
    COUNT(r.id) as active_reservations_count
FROM users u
LEFT JOIN reservations r ON u.id = r.user_id AND r.status = 'ACTIVE'
WHERE u.remaining_days <= 0 AND u.role != 'ADMIN'
GROUP BY u.id, u.username, u.name, u.remaining_days
ORDER BY u.id;

-- 6. 查看所有预约状态分布
SELECT 
    status,
    COUNT(*) as count
FROM reservations 
GROUP BY status
ORDER BY status;

-- 7. 查看剩余天数分布
SELECT 
    remaining_days,
    COUNT(*) as user_count
FROM users 
WHERE role != 'ADMIN'
GROUP BY remaining_days
ORDER BY remaining_days;

SELECT 'Immediate deletion of zero days user reservations completed!' as message;
