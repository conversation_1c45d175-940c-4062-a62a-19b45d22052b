package com.seatmaster.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@TableName("rooms")
public class Room {
    
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    @TableField("school_id")
    private Long schoolId;
    
    @TableField("name")
    private String name;
    
    @TableField("room_id")
    private String roomId;
    
    @TableField("max_reservation_hours")
    private BigDecimal maxReservationHours;
    
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    private LocalDateTime createdTime;
    
    @TableField("description")
    private String description;

    @TableField("version")
    private Integer version;

    @TableField("seat_id")
    private String seatId;
} 