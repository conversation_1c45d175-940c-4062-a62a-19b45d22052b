#!/bin/bash

# 房间管理API测试脚本

echo "=== 房间管理API测试 ==="

# 设置基础URL
BASE_URL="http://localhost:8080/api"

# 首先测试登录获取JWT token
echo "1. 测试管理员登录..."
LOGIN_RESPONSE=$(curl -s -X POST \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}' \
  ${BASE_URL}/auth/login)

echo "登录响应: $LOGIN_RESPONSE"

# 提取JWT token (假设响应格式为 {"code":200,"data":{"token":"xxx"}})
TOKEN=$(echo $LOGIN_RESPONSE | grep -o '"token":"[^"]*"' | cut -d'"' -f4)

if [ -z "$TOKEN" ]; then
    echo "❌ 登录失败，无法获取token"
    exit 1
fi

echo "✅ 登录成功，Token: ${TOKEN:0:20}..."

# 测试控制器是否可访问
echo ""
echo "2. 测试控制器连通性..."
TEST_RESPONSE=$(curl -s -H "Authorization: Bearer $TOKEN" \
  ${BASE_URL}/admin/room-management/test)

echo "测试响应: $TEST_RESPONSE"

# 测试获取学校列表
echo ""
echo "3. 测试获取学校列表..."
SCHOOLS_RESPONSE=$(curl -s -H "Authorization: Bearer $TOKEN" \
  ${BASE_URL}/admin/room-management/schools)

echo "学校列表响应: $SCHOOLS_RESPONSE"

# 测试获取房间列表
echo ""
echo "4. 测试获取房间列表..."
ROOMS_RESPONSE=$(curl -s -H "Authorization: Bearer $TOKEN" \
  ${BASE_URL}/admin/room-management/rooms)

echo "房间列表响应: $ROOMS_RESPONSE"

# 测试创建学校
echo ""
echo "5. 测试创建学校..."
CREATE_SCHOOL_RESPONSE=$(curl -s -X POST \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"name":"测试大学"}' \
  ${BASE_URL}/admin/room-management/schools)

echo "创建学校响应: $CREATE_SCHOOL_RESPONSE"

# 提取学校ID
SCHOOL_ID=$(echo $CREATE_SCHOOL_RESPONSE | grep -o '"id":[0-9]*' | cut -d':' -f2)

if [ ! -z "$SCHOOL_ID" ]; then
    echo "✅ 学校创建成功，ID: $SCHOOL_ID"
    
    # 测试创建房间
    echo ""
    echo "6. 测试创建房间..."
    CREATE_ROOM_RESPONSE=$(curl -s -X POST \
      -H "Authorization: Bearer $TOKEN" \
      -H "Content-Type: application/json" \
      -d "{\"schoolId\":$SCHOOL_ID,\"name\":\"测试房间\",\"roomId\":\"TEST-001\",\"totalSeats\":50,\"maxReservationHours\":8,\"description\":\"测试房间描述\"}" \
      ${BASE_URL}/admin/room-management/rooms)
    
    echo "创建房间响应: $CREATE_ROOM_RESPONSE"
    
    # 再次获取房间列表验证
    echo ""
    echo "7. 验证房间创建..."
    ROOMS_RESPONSE_2=$(curl -s -H "Authorization: Bearer $TOKEN" \
      ${BASE_URL}/admin/room-management/rooms)
    
    echo "更新后的房间列表: $ROOMS_RESPONSE_2"
else
    echo "❌ 学校创建失败"
fi

echo ""
echo "=== 测试完成 ==="
