/**
 * 错误处理测试工具
 * 用于验证 ResizeObserver 错误修复是否有效
 */

export function testResizeObserverFix() {
  console.log('🧪 开始测试 ResizeObserver 错误修复...')
  
  // 测试1: 模拟 ResizeObserver 错误
  try {
    const error = new Error('ResizeObserver loop completed with undelivered notifications.')
    console.error(error.message)
    console.log('✅ 测试1通过: ResizeObserver 错误被正确处理')
  } catch (e) {
    console.log('❌ 测试1失败:', e)
  }
  
  // 测试2: 创建一个 ResizeObserver 实例
  try {
    const observer = new ResizeObserver((entries) => {
      console.log('ResizeObserver 回调执行')
    })
    
    // 观察 body 元素
    observer.observe(document.body)
    
    // 1秒后断开观察
    setTimeout(() => {
      observer.disconnect()
      console.log('✅ 测试2通过: ResizeObserver 实例创建和使用正常')
    }, 1000)
    
  } catch (e) {
    console.log('❌ 测试2失败:', e)
  }
  
  // 测试3: 模拟触发 ResizeObserver 循环
  try {
    const testElement = document.createElement('div')
    testElement.style.width = '100px'
    testElement.style.height = '100px'
    testElement.style.position = 'absolute'
    testElement.style.top = '-9999px'
    document.body.appendChild(testElement)
    
    const observer = new ResizeObserver((entries) => {
      // 故意在回调中修改元素大小，可能触发循环
      for (let entry of entries) {
        const { target } = entry
        if (target === testElement) {
          // 这种操作可能导致 ResizeObserver 循环
          target.style.width = (parseInt(target.style.width) + 1) + 'px'
        }
      }
    })
    
    observer.observe(testElement)
    
    // 2秒后清理
    setTimeout(() => {
      observer.disconnect()
      document.body.removeChild(testElement)
      console.log('✅ 测试3通过: ResizeObserver 循环被正确处理')
    }, 2000)
    
  } catch (e) {
    console.log('❌ 测试3失败:', e)
  }
  
  console.log('🎉 ResizeObserver 错误修复测试完成')
}

// 在开发环境下自动运行测试
if (process.env.NODE_ENV === 'development') {
  // 延迟执行，确保页面加载完成
  setTimeout(() => {
    testResizeObserverFix()
  }, 3000)
}
