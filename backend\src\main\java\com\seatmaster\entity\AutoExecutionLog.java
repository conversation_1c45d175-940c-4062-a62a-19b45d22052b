package com.seatmaster.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 自动预约执行日志实体类
 * 对应数据库表：auto_execution_logs
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("auto_execution_logs")
public class AutoExecutionLog {
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 预约记录ID
     */
    private Long reservationId;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 执行时间
     */
    private LocalDateTime executionTime;
    
    /**
     * 执行状态：SUCCESS-成功, FAILED-失败, TIMEOUT-超时
     */
    private String executionStatus;
    
    /**
     * 执行结果信息
     */
    private String resultMessage;
    
    /**
     * 执行耗时(秒)
     */
    private Integer executionDuration;
    
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdTime;
    
    /**
     * 执行状态枚举
     */
    public enum ExecutionStatus {
        SUCCESS("SUCCESS", "执行成功"),
        FAILED("FAILED", "执行失败"),
        TIMEOUT("TIMEOUT", "执行超时");
        
        private final String code;
        private final String description;
        
        ExecutionStatus(String code, String description) {
            this.code = code;
            this.description = description;
        }
        
        public String getCode() {
            return code;
        }
        
        public String getDescription() {
            return description;
        }
    }
}
