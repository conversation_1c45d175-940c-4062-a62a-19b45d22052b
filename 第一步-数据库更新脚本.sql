-- 第一步：SeatMaster自动预约功能数据库更新脚本
-- 基于现有seat_reservation数据库结构

USE seat_reservation;

-- 检查当前数据库结构
SELECT 'Current reservations table structure:' as info;
DESCRIBE reservations;

SELECT 'Current users table structure:' as info;
DESCRIBE users;

-- 1. 添加必要字段到reservations表
-- 检查字段是否已存在，避免重复添加
SET @sql = (SELECT IF(
    EXISTS(
        SELECT * FROM information_schema.COLUMNS 
        WHERE TABLE_SCHEMA = 'seat_reservation' 
        AND TABLE_NAME = 'reservations' 
        AND COLUMN_NAME = 'last_execution_time'
    ),
    'SELECT "last_execution_time field already exists" as message',
    'ALTER TABLE reservations ADD COLUMN last_execution_time TIMESTAMP NULL COMMENT "最后执行时间"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    EXISTS(
        SELECT * FROM information_schema.COLUMNS 
        WHERE TABLE_SCHEMA = 'seat_reservation' 
        AND TABLE_NAME = 'reservations' 
        AND COLUMN_NAME = 'execution_result'
    ),
    'SELECT "execution_result field already exists" as message',
    'ALTER TABLE reservations ADD COLUMN execution_result TEXT COMMENT "执行结果(JSON)"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 2. 扩展status枚举，添加自动预约状态
-- 当前status: 'ACTIVE', 'CANCELLED', 'PAUSED'
-- 需要添加: 'AUTO_PENDING', 'AUTO_SUCCESS', 'AUTO_FAILED'
ALTER TABLE reservations MODIFY status ENUM('ACTIVE', 'CANCELLED', 'PAUSED', 'AUTO_PENDING', 'AUTO_SUCCESS', 'AUTO_FAILED') NOT NULL DEFAULT 'ACTIVE';

-- 3. 创建执行日志表
CREATE TABLE IF NOT EXISTS auto_execution_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    reservation_id BIGINT NOT NULL COMMENT '预约记录ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    execution_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '执行时间',
    execution_status ENUM('SUCCESS', 'FAILED', 'TIMEOUT') NOT NULL,
    result_message TEXT COMMENT '执行结果信息',
    execution_duration INT COMMENT '执行耗时(秒)',
    
    INDEX idx_reservation_time (reservation_id, execution_time),
    INDEX idx_user_status (user_id, execution_status),
    FOREIGN KEY (reservation_id) REFERENCES reservations(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) COMMENT '自动预约执行日志表';

-- 4. 验证更改
SELECT 'Updated reservations table structure:' as info;
DESCRIBE reservations;

SELECT 'New auto_execution_logs table structure:' as info;
DESCRIBE auto_execution_logs;

-- 5. 检查status枚举值
SELECT 'Status enum values:' as info;
SHOW COLUMNS FROM reservations LIKE 'status';

-- 6. 测试数据插入 (验证表结构正确)
-- 注意：这里只是测试，不会实际插入数据
SELECT 'Testing table structure...' as info;

-- 测试reservations表新字段
SELECT 
    id, 
    user_id, 
    status, 
    last_execution_time, 
    execution_result 
FROM reservations 
LIMIT 1;

-- 测试auto_execution_logs表
SELECT COUNT(*) as log_count FROM auto_execution_logs;

-- 7. 创建索引优化查询性能
CREATE INDEX IF NOT EXISTS idx_reservations_auto_status ON reservations(status, reservation_open_time) 
WHERE status IN ('AUTO_PENDING', 'AUTO_SUCCESS', 'AUTO_FAILED');

CREATE INDEX IF NOT EXISTS idx_reservations_execution_time ON reservations(last_execution_time);

-- 8. 显示完成信息
SELECT 'Database update completed successfully!' as result;
SELECT 'Added fields:' as info;
SELECT '- last_execution_time (TIMESTAMP)' as field1;
SELECT '- execution_result (TEXT)' as field2;
SELECT 'Extended status enum with:' as info;
SELECT '- AUTO_PENDING' as status1;
SELECT '- AUTO_SUCCESS' as status2;
SELECT '- AUTO_FAILED' as status3;
SELECT 'Created table: auto_execution_logs' as table_info;

COMMIT;

-- 完成第一步！
-- 总结：
-- ✅ 添加了2个字段到reservations表
-- ✅ 扩展了status枚举，添加了3个自动预约状态
-- ✅ 创建了auto_execution_logs日志表
-- ✅ 添加了必要的索引优化性能
-- ✅ 完全兼容现有数据结构
-- ✅ 使用了安全的字段检查，避免重复添加
