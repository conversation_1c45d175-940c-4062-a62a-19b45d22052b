package com.seatmaster.dto;

import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * 自动预约请求DTO
 * 用于接收用户创建自动预约的请求参数
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AutoReservationRequest {
    
    /**
     * 房间ID
     */
    @NotNull(message = "房间ID不能为空")
    private Long roomId;
    
    /**
     * 座位ID
     */
    @NotBlank(message = "座位ID不能为空")
    private String seatId;
    
    /**
     * 开始时间 (格式: HH:mm)
     */
    @NotBlank(message = "开始时间不能为空")
    @Pattern(regexp = "^([01]?[0-9]|2[0-3]):[0-5][0-9]$", message = "开始时间格式错误，应为HH:mm")
    private String startTime;
    
    /**
     * 结束时间 (格式: HH:mm)
     */
    @NotBlank(message = "结束时间不能为空")
    @Pattern(regexp = "^([01]?[0-9]|2[0-3]):[0-5][0-9]$", message = "结束时间格式错误，应为HH:mm")
    private String endTime;
    
    /**
     * 预约执行时间 (格式: HH:mm:ss)
     * 系统将在每天的这个时间点执行自动预约
     */
    @NotBlank(message = "预约执行时间不能为空")
    @Pattern(regexp = "^([01]?[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]$", message = "预约执行时间格式错误，应为HH:mm:ss")
    private String reservationOpenTime;
    
    /**
     * 预约类型 (可选，默认为ADVANCE_ONE_DAY)
     * SAME_DAY: 当天预约
     * ADVANCE_ONE_DAY: 提前一天预约
     */
    private String reservationType = "ADVANCE_ONE_DAY";
    
    /**
     * 备注信息 (可选)
     */
    private String remark;
    
    /**
     * 验证时间格式是否正确
     * 
     * @return 是否有效
     */
    public boolean isValidTimeFormat() {
        try {
            // 验证开始时间和结束时间的逻辑关系
            String[] startParts = startTime.split(":");
            String[] endParts = endTime.split(":");
            
            int startHour = Integer.parseInt(startParts[0]);
            int startMinute = Integer.parseInt(startParts[1]);
            int endHour = Integer.parseInt(endParts[0]);
            int endMinute = Integer.parseInt(endParts[1]);
            
            int startTotalMinutes = startHour * 60 + startMinute;
            int endTotalMinutes = endHour * 60 + endMinute;
            
            // 结束时间必须晚于开始时间
            return endTotalMinutes > startTotalMinutes;
            
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 获取预约时长(分钟)
     * 
     * @return 预约时长
     */
    public int getReservationDurationMinutes() {
        if (!isValidTimeFormat()) {
            return 0;
        }
        
        try {
            String[] startParts = startTime.split(":");
            String[] endParts = endTime.split(":");
            
            int startHour = Integer.parseInt(startParts[0]);
            int startMinute = Integer.parseInt(startParts[1]);
            int endHour = Integer.parseInt(endParts[0]);
            int endMinute = Integer.parseInt(endParts[1]);
            
            int startTotalMinutes = startHour * 60 + startMinute;
            int endTotalMinutes = endHour * 60 + endMinute;
            
            return endTotalMinutes - startTotalMinutes;
            
        } catch (Exception e) {
            return 0;
        }
    }
}
