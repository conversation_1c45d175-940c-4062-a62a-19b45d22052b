# 分布式座位预约系统设计方案

## 1. 系统概述

基于现有的Spring Boot + Vue架构，设计一个主从式分布式座位预约系统，实现高可用、高并发的座位预约服务。

### 1.1 设计目标
- 基于现有架构扩展，最小化代码变更
- 实现主从式架构：中央控制服务器 + 多个执行服务器
- 保持用户体验一致，分布式处理对用户透明
- 支持高并发座位预约，避免超售问题
- 提供故障转移和负载均衡能力

### 1.2 技术栈
- **后端**: Spring Boot 2.7 + Spring Cloud Gateway + Spring Cloud LoadBalancer
- **前端**: Vue 3 + Element Plus
- **数据库**: MySQL (主从复制) + Redis (分布式缓存)
- **消息队列**: RabbitMQ (异步处理)
- **服务发现**: Eureka Server
- **配置管理**: Spring Cloud Config

## 2. 系统架构设计

### 2.1 整体架构图

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Vue Frontend  │    │   Vue Frontend  │    │   Vue Frontend  │
│   (Client 1)    │    │   (Client 2)    │    │   (Client N)    │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────▼─────────────┐
                    │   Spring Cloud Gateway   │
                    │   (API Gateway/LB)       │
                    └─────────────┬─────────────┘
                                 │
          ┌──────────────────────┼──────────────────────┐
          │                      │                      │
┌─────────▼───────┐    ┌─────────▼───────┐    ┌─────────▼───────┐
│ Reservation     │    │ Reservation     │    │ Reservation     │
│ Service Node 1  │    │ Service Node 2  │    │ Service Node N  │
│ (Spring Boot)   │    │ (Spring Boot)   │    │ (Spring Boot)   │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────▼─────────────┐
                    │     Central Control       │
                    │     Service (Master)      │
                    │    (Spring Boot)          │
                    └─────────────┬─────────────┘
                                 │
          ┌──────────────────────┼──────────────────────┐
          │                      │                      │
┌─────────▼───────┐    ┌─────────▼───────┐    ┌─────────▼───────┐
│ MySQL Master    │    │ Redis Cluster   │    │ RabbitMQ        │
│                 │    │ (Cache)         │    │ (Message Queue) │
└─────────────────┘    └─────────────────┘    └─────────────────┘
          │
┌─────────▼───────┐
│ MySQL Slave     │
│ (Read Replica)  │
└─────────────────┘
```

### 2.2 服务组件说明

#### 2.2.1 API Gateway (Spring Cloud Gateway)
- 统一入口，负载均衡
- 路由转发和请求过滤
- 限流和熔断保护
- 跨域处理

#### 2.2.2 Reservation Service Nodes (执行服务器)
- 处理座位预约请求
- 本地缓存热点数据
- 与中央控制服务通信
- 无状态设计，支持水平扩展

#### 2.2.3 Central Control Service (中央控制服务器)
- 座位状态管理
- 分布式锁协调
- 数据一致性保证
- 配置管理和服务监控

## 3. 数据库设计扩展

### 3.1 最小化数据库变更

在现有表基础上，仅添加必要字段：

```sql
-- 扩展 reservations 表
ALTER TABLE reservations ADD COLUMN node_id VARCHAR(50) COMMENT '处理节点ID';
ALTER TABLE reservations ADD COLUMN lock_version INT DEFAULT 0 COMMENT '乐观锁版本';
ALTER TABLE reservations ADD COLUMN created_node VARCHAR(50) COMMENT '创建节点';

-- 扩展 rooms 表  
ALTER TABLE rooms ADD COLUMN node_assignment VARCHAR(200) COMMENT '节点分配策略';
ALTER TABLE rooms ADD COLUMN last_sync_time TIMESTAMP COMMENT '最后同步时间';

-- 新增分布式锁表
CREATE TABLE distributed_locks (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    lock_key VARCHAR(255) UNIQUE NOT NULL,
    lock_value VARCHAR(255) NOT NULL,
    expire_time TIMESTAMP NOT NULL,
    node_id VARCHAR(50) NOT NULL,
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 新增节点状态表
CREATE TABLE service_nodes (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    node_id VARCHAR(50) UNIQUE NOT NULL,
    node_name VARCHAR(100) NOT NULL,
    node_status ENUM('ACTIVE', 'INACTIVE', 'MAINTENANCE') DEFAULT 'ACTIVE',
    last_heartbeat TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    load_factor DECIMAL(5,2) DEFAULT 0.00,
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 3.2 Redis 缓存设计

```
# 座位状态缓存
seat:status:{roomId}:{seatId} -> {status, userId, expireTime}

# 房间可用座位计数
room:available:{roomId} -> count

# 用户预约锁
user:reservation:lock:{userId} -> {lockTime, nodeId}

# 节点负载信息
node:load:{nodeId} -> {currentLoad, maxCapacity}

# 分布式配置
config:reservation -> {maxReservationTime, cleanupInterval}
```

## 4. 核心服务实现

### 4.1 API Gateway 配置

```yaml
# application.yml
spring:
  cloud:
    gateway:
      routes:
        - id: reservation-service
          uri: lb://reservation-service
          predicates:
            - Path=/api/reservations/**
          filters:
            - name: CircuitBreaker
              args:
                name: reservationCircuitBreaker
                fallbackUri: forward:/fallback/reservation
            - name: RequestRateLimiter
              args:
                rate-limiter: "#{@redisRateLimiter}"
                key-resolver: "#{@userKeyResolver}"
        
        - id: central-control
          uri: lb://central-control-service
          predicates:
            - Path=/api/admin/**,/api/control/**
          filters:
            - name: CircuitBreaker
              args:
                name: controlCircuitBreaker
                fallbackUri: forward:/fallback/control

      discovery:
        locator:
          enabled: true
          lower-case-service-id: true

  redis:
    host: localhost
    port: 6379
    database: 0
    timeout: 2000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,gateway
  endpoint:
    health:
      show-details: always
```

### 4.2 Reservation Service Node 实现

```java
@RestController
@RequestMapping("/api/reservations")
public class DistributedReservationController {
    
    @Autowired
    private DistributedReservationService reservationService;
    
    @Autowired
    private LoadBalancerService loadBalancerService;
    
    @PostMapping("/create")
    public ResponseEntity<ReservationResponse> createReservation(
            @RequestBody ReservationRequest request) {
        
        // 检查节点负载
        if (!loadBalancerService.canAcceptRequest()) {
            return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE)
                .body(new ReservationResponse("服务繁忙，请稍后重试"));
        }
        
        try {
            ReservationResponse response = reservationService
                .createDistributedReservation(request);
            return ResponseEntity.ok(response);
        } catch (SeatUnavailableException e) {
            return ResponseEntity.badRequest()
                .body(new ReservationResponse("座位不可用"));
        } catch (DistributedLockException e) {
            return ResponseEntity.status(HttpStatus.CONFLICT)
                .body(new ReservationResponse("系统繁忙，请重试"));
        }
    }
    
    @GetMapping("/status/{roomId}")
    public ResponseEntity<RoomStatusResponse> getRoomStatus(
            @PathVariable Long roomId) {
        
        RoomStatusResponse status = reservationService
            .getDistributedRoomStatus(roomId);
        return ResponseEntity.ok(status);
    }
}

@Service
public class DistributedReservationService {
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    @Autowired
    private CentralControlClient centralControlClient;
    
    @Autowired
    private DistributedLockService lockService;
    
    @Value("${spring.application.instance-id}")
    private String nodeId;
    
    @Transactional
    public ReservationResponse createDistributedReservation(
            ReservationRequest request) {
        
        String lockKey = "reservation:" + request.getUserId();
        
        // 获取分布式锁
        if (!lockService.tryLock(lockKey, 30, TimeUnit.SECONDS)) {
            throw new DistributedLockException("无法获取预约锁");
        }
        
        try {
            // 检查座位状态（先查缓存）
            String seatKey = "seat:status:" + request.getRoomId() 
                + ":" + request.getSeatId();
            Object seatStatus = redisTemplate.opsForValue().get(seatKey);
            
            if (seatStatus != null && !"AVAILABLE".equals(seatStatus)) {
                throw new SeatUnavailableException("座位已被预约");
            }
            
            // 向中央控制服务请求预约
            ReservationRequest distributedRequest = request.toBuilder()
                .nodeId(nodeId)
                .build();
                
            ReservationResponse response = centralControlClient
                .requestReservation(distributedRequest);
            
            if (response.isSuccess()) {
                // 更新本地缓存
                updateLocalCache(request, "RESERVED");
                
                // 异步更新数据库
                asyncUpdateDatabase(request, nodeId);
            }
            
            return response;
            
        } finally {
            lockService.unlock(lockKey);
        }
    }
    
    private void updateLocalCache(ReservationRequest request, String status) {
        String seatKey = "seat:status:" + request.getRoomId() 
            + ":" + request.getSeatId();
        
        SeatStatus seatStatus = SeatStatus.builder()
            .status(status)
            .userId(request.getUserId())
            .expireTime(System.currentTimeMillis() + 
                Duration.ofHours(2).toMillis())
            .build();
            
        redisTemplate.opsForValue().set(seatKey, seatStatus, 
            Duration.ofHours(3));
    }
    
    @Async
    private void asyncUpdateDatabase(ReservationRequest request, String nodeId) {
        // 异步更新数据库，提高响应速度
        reservationRepository.save(Reservation.builder()
            .userId(request.getUserId())
            .roomId(request.getRoomId())
            .seatId(request.getSeatId())
            .nodeId(nodeId)
            .createdNode(nodeId)
            .build());
    }
}
```

### 4.3 Central Control Service 实现

```java
@RestController
@RequestMapping("/api/control")
public class CentralControlController {
    
    @Autowired
    private CentralReservationService centralService;
    
    @PostMapping("/reservation/request")
    public ResponseEntity<ReservationResponse> processReservationRequest(
            @RequestBody ReservationRequest request) {
        
        ReservationResponse response = centralService
            .processDistributedReservation(request);
        return ResponseEntity.ok(response);
    }
    
    @GetMapping("/nodes/status")
    public ResponseEntity<List<NodeStatus>> getNodesStatus() {
        List<NodeStatus> nodes = centralService.getAllNodesStatus();
        return ResponseEntity.ok(nodes);
    }
}

@Service
public class CentralReservationService {
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    @Autowired
    private ReservationRepository reservationRepository;
    
    @Autowired
    private DistributedLockService globalLockService;
    
    @Autowired
    private RabbitTemplate rabbitTemplate;
    
    public ReservationResponse processDistributedReservation(
            ReservationRequest request) {
        
        String globalLockKey = "global:seat:" + request.getRoomId() 
            + ":" + request.getSeatId();
        
        // 获取全局座位锁
        if (!globalLockService.tryLock(globalLockKey, 10, TimeUnit.SECONDS)) {
            return ReservationResponse.failure("座位正在被处理");
        }
        
        try {
            // 检查座位最新状态
            boolean isAvailable = checkSeatAvailability(
                request.getRoomId(), request.getSeatId());
            
            if (!isAvailable) {
                return ReservationResponse.failure("座位不可用");
            }
            
            // 创建预约记录
            Reservation reservation = createReservation(request);
            
            // 更新全局缓存
            updateGlobalCache(request, "RESERVED");
            
            // 发送同步消息到所有节点
            broadcastSeatStatusUpdate(request, "RESERVED");
            
            return ReservationResponse.success(reservation.getId());
            
        } finally {
            globalLockService.unlock(globalLockKey);
        }
    }
    
    private void broadcastSeatStatusUpdate(ReservationRequest request, 
            String status) {
        
        SeatStatusUpdateMessage message = SeatStatusUpdateMessage.builder()
            .roomId(request.getRoomId())
            .seatId(request.getSeatId())
            .status(status)
            .userId(request.getUserId())
            .timestamp(System.currentTimeMillis())
            .build();
        
        rabbitTemplate.convertAndSend("seat.status.exchange", 
            "seat.status.update", message);
    }
}
```

## 5. 前端适配

### 5.1 最小化前端变更

前端保持现有接口不变，通过API Gateway透明路由：

```javascript
// 现有的预约服务调用保持不变
export const reservationApi = {
  // 创建预约 - 路由到分布式服务
  createReservation(data) {
    return request({
      url: '/api/reservations/create',  // 自动路由到负载均衡的节点
      method: 'post',
      data
    })
  },
  
  // 获取房间状态 - 从最近的节点获取
  getRoomStatus(roomId) {
    return request({
      url: `/api/reservations/status/${roomId}`,
      method: 'get'
    })
  },
  
  // 管理功能 - 路由到中央控制服务
  getSystemStatus() {
    return request({
      url: '/api/control/nodes/status',
      method: 'get'
    })
  }
}
```

### 5.2 添加系统监控界面

```vue
<!-- SystemMonitor.vue -->
<template>
  <div class="system-monitor">
    <el-card header="分布式系统状态">
      <el-row :gutter="20">
        <el-col :span="8" v-for="node in nodes" :key="node.nodeId">
          <el-card :class="getNodeStatusClass(node.status)">
            <h4>{{ node.nodeName }}</h4>
            <p>状态: {{ node.status }}</p>
            <p>负载: {{ node.loadFactor }}%</p>
            <p>最后心跳: {{ formatTime(node.lastHeartbeat) }}</p>
          </el-card>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
export default {
  name: 'SystemMonitor',
  data() {
    return {
      nodes: [],
      timer: null
    }
  },
  mounted() {
    this.loadNodesStatus()
    this.timer = setInterval(this.loadNodesStatus, 5000)
  },
  beforeUnmount() {
    if (this.timer) {
      clearInterval(this.timer)
    }
  },
  methods: {
    async loadNodesStatus() {
      try {
        const response = await reservationApi.getSystemStatus()
        this.nodes = response.data
      } catch (error) {
        console.error('加载节点状态失败:', error)
      }
    },
    getNodeStatusClass(status) {
      return {
        'node-active': status === 'ACTIVE',
        'node-inactive': status === 'INACTIVE',
        'node-maintenance': status === 'MAINTENANCE'
      }
    },
    formatTime(timestamp) {
      return new Date(timestamp).toLocaleString()
    }
  }
}
</script>
```

## 6. 部署配置

### 6.1 Docker Compose 配置

```yaml
# docker-compose.yml
version: '3.8'

services:
  # API Gateway
  gateway:
    build: ./gateway
    ports:
      - "8080:8080"
    environment:
      - EUREKA_CLIENT_SERVICE_URL_DEFAULTZONE=http://eureka:8761/eureka
    depends_on:
      - eureka
      - redis
    networks:
      - seat-network

  # Eureka Server
  eureka:
    build: ./eureka-server
    ports:
      - "8761:8761"
    networks:
      - seat-network

  # Central Control Service
  central-control:
    build: ./central-control
    environment:
      - SPRING_DATASOURCE_URL=*****************************************
      - SPRING_REDIS_HOST=redis
      - EUREKA_CLIENT_SERVICE_URL_DEFAULTZONE=http://eureka:8761/eureka
    depends_on:
      - mysql-master
      - redis
      - rabbitmq
      - eureka
    networks:
      - seat-network

  # Reservation Service Nodes
  reservation-node-1:
    build: ./reservation-service
    environment:
      - SPRING_APPLICATION_INSTANCE_ID=node-1
      - SPRING_DATASOURCE_URL=****************************************
      - SPRING_REDIS_HOST=redis
      - EUREKA_CLIENT_SERVICE_URL_DEFAULTZONE=http://eureka:8761/eureka
    depends_on:
      - mysql-slave
      - redis
      - rabbitmq
      - eureka
    networks:
      - seat-network

  reservation-node-2:
    build: ./reservation-service
    environment:
      - SPRING_APPLICATION_INSTANCE_ID=node-2
      - SPRING_DATASOURCE_URL=****************************************
      - SPRING_REDIS_HOST=redis
      - EUREKA_CLIENT_SERVICE_URL_DEFAULTZONE=http://eureka:8761/eureka
    depends_on:
      - mysql-slave
      - redis
      - rabbitmq
      - eureka
    networks:
      - seat-network

  # MySQL Master
  mysql-master:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=root
      - MYSQL_DATABASE=seatmaster
    volumes:
      - mysql-master-data:/var/lib/mysql
      - ./mysql/master.cnf:/etc/mysql/conf.d/master.cnf
    networks:
      - seat-network

  # MySQL Slave
  mysql-slave:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=root
      - MYSQL_DATABASE=seatmaster
    volumes:
      - mysql-slave-data:/var/lib/mysql
      - ./mysql/slave.cnf:/etc/mysql/conf.d/slave.cnf
    depends_on:
      - mysql-master
    networks:
      - seat-network

  # Redis
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    networks:
      - seat-network

  # RabbitMQ
  rabbitmq:
    image: rabbitmq:3-management
    ports:
      - "5672:5672"
      - "15672:15672"
    environment:
      - RABBITMQ_DEFAULT_USER=admin
      - RABBITMQ_DEFAULT_PASS=admin
    volumes:
      - rabbitmq-data:/var/lib/rabbitmq
    networks:
      - seat-network

  # Frontend
  frontend:
    build: ./frontend
    ports:
      - "80:80"
    depends_on:
      - gateway
    networks:
      - seat-network

volumes:
  mysql-master-data:
  mysql-slave-data:
  redis-data:
  rabbitmq-data:

networks:
  seat-network:
    driver: bridge
```

## 7. 总结

本设计方案基于现有Spring Boot + Vue架构，通过引入Spring Cloud Gateway、Redis缓存、RabbitMQ消息队列等组件，实现了分布式座位预约系统。主要特点：

1. **最小化变更**: 保持现有代码结构，仅添加分布式相关功能
2. **透明分布式**: 用户体验保持一致，分布式处理对前端透明
3. **高可用性**: 多节点部署，支持故障转移
4. **高并发**: 通过缓存和分布式锁解决并发问题
5. **可扩展**: 支持动态添加服务节点

该方案可以有效解决单体应用的性能瓶颈，提供更好的用户体验和系统稳定性。

## 8. 关键技术实现细节

### 8.1 分布式锁实现

```java
@Component
public class RedisDistributedLockService implements DistributedLockService {

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Value("${spring.application.instance-id}")
    private String nodeId;

    private static final String LOCK_PREFIX = "distributed:lock:";
    private static final String UNLOCK_SCRIPT =
        "if redis.call('get', KEYS[1]) == ARGV[1] then " +
        "return redis.call('del', KEYS[1]) else return 0 end";

    @Override
    public boolean tryLock(String key, long timeout, TimeUnit unit) {
        String lockKey = LOCK_PREFIX + key;
        String lockValue = nodeId + ":" + Thread.currentThread().getId()
            + ":" + System.currentTimeMillis();

        Boolean result = redisTemplate.opsForValue()
            .setIfAbsent(lockKey, lockValue, timeout, unit);

        if (Boolean.TRUE.equals(result)) {
            // 设置锁的过期时间，防止死锁
            redisTemplate.expire(lockKey, timeout, unit);
            return true;
        }

        return false;
    }

    @Override
    public void unlock(String key) {
        String lockKey = LOCK_PREFIX + key;
        String lockValue = nodeId + ":" + Thread.currentThread().getId();

        // 使用Lua脚本保证原子性
        DefaultRedisScript<Long> script = new DefaultRedisScript<>();
        script.setScriptText(UNLOCK_SCRIPT);
        script.setResultType(Long.class);

        redisTemplate.execute(script, Collections.singletonList(lockKey), lockValue);
    }
}
```

### 8.2 负载均衡策略

```java
@Component
public class LoadBalancerService {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Value("${reservation.node.max-capacity:100}")
    private int maxCapacity;

    @Value("${spring.application.instance-id}")
    private String nodeId;

    private final AtomicInteger currentLoad = new AtomicInteger(0);

    public boolean canAcceptRequest() {
        int current = currentLoad.get();
        if (current >= maxCapacity) {
            return false;
        }

        // 更新节点负载信息到Redis
        updateNodeLoad(current + 1);
        currentLoad.incrementAndGet();

        return true;
    }

    public void releaseRequest() {
        int current = currentLoad.decrementAndGet();
        updateNodeLoad(Math.max(0, current));
    }

    private void updateNodeLoad(int load) {
        String nodeKey = "node:load:" + nodeId;
        NodeLoadInfo loadInfo = NodeLoadInfo.builder()
            .nodeId(nodeId)
            .currentLoad(load)
            .maxCapacity(maxCapacity)
            .timestamp(System.currentTimeMillis())
            .build();

        redisTemplate.opsForValue().set(nodeKey, loadInfo, Duration.ofMinutes(5));
    }

    @Scheduled(fixedRate = 30000) // 每30秒更新一次心跳
    public void updateHeartbeat() {
        String heartbeatKey = "node:heartbeat:" + nodeId;
        redisTemplate.opsForValue().set(heartbeatKey,
            System.currentTimeMillis(), Duration.ofMinutes(2));
    }
}
```

### 8.3 消息队列处理

```java
@Component
public class SeatStatusMessageHandler {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private ReservationRepository reservationRepository;

    @RabbitListener(queues = "seat.status.update.queue")
    public void handleSeatStatusUpdate(SeatStatusUpdateMessage message) {
        try {
            // 更新本地缓存
            updateLocalSeatCache(message);

            // 更新房间可用座位计数
            updateRoomAvailableCount(message);

            // 记录日志
            log.info("处理座位状态更新: roomId={}, seatId={}, status={}",
                message.getRoomId(), message.getSeatId(), message.getStatus());

        } catch (Exception e) {
            log.error("处理座位状态更新失败", e);
            // 发送到死信队列进行重试
            throw new AmqpRejectAndDontRequeueException("处理失败", e);
        }
    }

    private void updateLocalSeatCache(SeatStatusUpdateMessage message) {
        String seatKey = "seat:status:" + message.getRoomId()
            + ":" + message.getSeatId();

        SeatStatus status = SeatStatus.builder()
            .status(message.getStatus())
            .userId(message.getUserId())
            .timestamp(message.getTimestamp())
            .build();

        if ("AVAILABLE".equals(message.getStatus())) {
            redisTemplate.delete(seatKey);
        } else {
            redisTemplate.opsForValue().set(seatKey, status, Duration.ofHours(3));
        }
    }

    private void updateRoomAvailableCount(SeatStatusUpdateMessage message) {
        String countKey = "room:available:" + message.getRoomId();

        if ("AVAILABLE".equals(message.getStatus())) {
            redisTemplate.opsForValue().increment(countKey);
        } else if ("RESERVED".equals(message.getStatus())) {
            redisTemplate.opsForValue().decrement(countKey);
        }
    }
}

@Configuration
@EnableRabbit
public class RabbitMQConfig {

    @Bean
    public TopicExchange seatStatusExchange() {
        return new TopicExchange("seat.status.exchange", true, false);
    }

    @Bean
    public Queue seatStatusUpdateQueue() {
        return QueueBuilder.durable("seat.status.update.queue")
            .withArgument("x-dead-letter-exchange", "seat.status.dlx")
            .withArgument("x-dead-letter-routing-key", "seat.status.dlq")
            .build();
    }

    @Bean
    public Binding seatStatusBinding() {
        return BindingBuilder.bind(seatStatusUpdateQueue())
            .to(seatStatusExchange())
            .with("seat.status.update");
    }

    @Bean
    public RabbitTemplate rabbitTemplate(ConnectionFactory connectionFactory) {
        RabbitTemplate template = new RabbitTemplate(connectionFactory);
        template.setMessageConverter(new Jackson2JsonMessageConverter());
        return template;
    }
}
```

### 8.4 健康检查和监控

```java
@Component
public class DistributedSystemHealthIndicator implements HealthIndicator {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private CentralControlClient centralControlClient;

    @Override
    public Health health() {
        Health.Builder builder = new Health.Builder();

        try {
            // 检查Redis连接
            redisTemplate.opsForValue().get("health:check");
            builder.withDetail("redis", "UP");

            // 检查中央控制服务连接
            boolean centralServiceUp = centralControlClient.healthCheck();
            builder.withDetail("central-control", centralServiceUp ? "UP" : "DOWN");

            // 检查当前节点负载
            int currentLoad = getCurrentNodeLoad();
            builder.withDetail("current-load", currentLoad);
            builder.withDetail("max-capacity", getMaxCapacity());

            // 检查数据库连接
            boolean dbUp = checkDatabaseConnection();
            builder.withDetail("database", dbUp ? "UP" : "DOWN");

            // 综合判断健康状态
            if (centralServiceUp && dbUp && currentLoad < getMaxCapacity() * 0.9) {
                builder.up();
            } else {
                builder.down();
            }

        } catch (Exception e) {
            builder.down().withException(e);
        }

        return builder.build();
    }

    private int getCurrentNodeLoad() {
        // 实现获取当前节点负载的逻辑
        return 0;
    }

    private int getMaxCapacity() {
        // 实现获取最大容量的逻辑
        return 100;
    }

    private boolean checkDatabaseConnection() {
        // 实现数据库连接检查的逻辑
        return true;
    }
}

@RestController
@RequestMapping("/actuator")
public class CustomActuatorController {

    @Autowired
    private LoadBalancerService loadBalancerService;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @GetMapping("/distributed-metrics")
    public ResponseEntity<Map<String, Object>> getDistributedMetrics() {
        Map<String, Object> metrics = new HashMap<>();

        // 节点负载信息
        metrics.put("node-load", loadBalancerService.getCurrentLoad());
        metrics.put("max-capacity", loadBalancerService.getMaxCapacity());

        // Redis缓存统计
        metrics.put("cache-hit-rate", getCacheHitRate());

        // 预约处理统计
        metrics.put("reservations-processed", getReservationsProcessed());
        metrics.put("reservations-failed", getReservationsFailed());

        return ResponseEntity.ok(metrics);
    }

    private double getCacheHitRate() {
        // 实现缓存命中率计算
        return 0.95;
    }

    private long getReservationsProcessed() {
        // 实现预约处理数量统计
        return 1000L;
    }

    private long getReservationsFailed() {
        // 实现预约失败数量统计
        return 10L;
    }
}
```

## 9. 性能优化策略

### 9.1 缓存策略

```java
@Service
public class CacheOptimizationService {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    // 多级缓存：本地缓存 + Redis缓存
    private final Cache<String, Object> localCache = Caffeine.newBuilder()
        .maximumSize(1000)
        .expireAfterWrite(5, TimeUnit.MINUTES)
        .build();

    public Object getWithMultiLevelCache(String key) {
        // 先查本地缓存
        Object value = localCache.getIfPresent(key);
        if (value != null) {
            return value;
        }

        // 再查Redis缓存
        value = redisTemplate.opsForValue().get(key);
        if (value != null) {
            localCache.put(key, value);
            return value;
        }

        return null;
    }

    public void putWithMultiLevelCache(String key, Object value, Duration ttl) {
        // 同时更新本地缓存和Redis缓存
        localCache.put(key, value);
        redisTemplate.opsForValue().set(key, value, ttl);
    }

    // 预热热点数据
    @EventListener(ApplicationReadyEvent.class)
    public void preloadHotData() {
        // 预加载热门房间的座位状态
        List<Long> hotRoomIds = getHotRoomIds();
        for (Long roomId : hotRoomIds) {
            preloadRoomSeats(roomId);
        }
    }

    private void preloadRoomSeats(Long roomId) {
        // 批量加载房间座位状态到缓存
        List<Seat> seats = seatRepository.findByRoomId(roomId);
        for (Seat seat : seats) {
            String key = "seat:status:" + roomId + ":" + seat.getId();
            SeatStatus status = getCurrentSeatStatus(seat);
            redisTemplate.opsForValue().set(key, status, Duration.ofHours(2));
        }
    }
}
```

### 9.2 数据库优化

```sql
-- 添加必要的索引
CREATE INDEX idx_reservations_room_seat ON reservations(room_id, seat_id);
CREATE INDEX idx_reservations_user_time ON reservations(user_id, start_time);
CREATE INDEX idx_reservations_node ON reservations(node_id, created_time);
CREATE INDEX idx_distributed_locks_key ON distributed_locks(lock_key);
CREATE INDEX idx_service_nodes_status ON service_nodes(node_status, last_heartbeat);

-- 分区表优化（按时间分区）
ALTER TABLE reservations PARTITION BY RANGE (YEAR(created_time)) (
    PARTITION p2024 VALUES LESS THAN (2025),
    PARTITION p2025 VALUES LESS THAN (2026),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
```

## 10. 故障处理和恢复

### 10.1 故障检测

```java
@Component
public class FailureDetectionService {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Scheduled(fixedRate = 30000) // 每30秒检查一次
    public void detectFailedNodes() {
        Set<String> allNodes = getAllRegisteredNodes();
        long currentTime = System.currentTimeMillis();

        for (String nodeId : allNodes) {
            String heartbeatKey = "node:heartbeat:" + nodeId;
            Object lastHeartbeat = redisTemplate.opsForValue().get(heartbeatKey);

            if (lastHeartbeat == null ||
                currentTime - (Long) lastHeartbeat > 120000) { // 2分钟无心跳
                handleNodeFailure(nodeId);
            }
        }
    }

    private void handleNodeFailure(String nodeId) {
        log.warn("检测到节点故障: {}", nodeId);

        // 标记节点为不可用
        markNodeAsUnavailable(nodeId);

        // 重新分配该节点的预约请求
        redistributeNodeReservations(nodeId);

        // 发送告警通知
        sendFailureAlert(nodeId);
    }

    private void redistributeNodeReservations(String failedNodeId) {
        // 查找该节点正在处理的预约
        List<Reservation> activeReservations = reservationRepository
            .findByNodeIdAndStatus(failedNodeId, "PROCESSING");

        for (Reservation reservation : activeReservations) {
            // 重新分配到其他可用节点
            String newNodeId = selectAvailableNode();
            if (newNodeId != null) {
                reservation.setNodeId(newNodeId);
                reservationRepository.save(reservation);

                // 通知新节点接管处理
                notifyNodeTakeOver(newNodeId, reservation);
            }
        }
    }
}
```

### 10.2 自动恢复机制

```java
@Component
public class AutoRecoveryService {

    @EventListener
    public void handleNodeRecovery(NodeRecoveryEvent event) {
        String nodeId = event.getNodeId();
        log.info("节点恢复: {}", nodeId);

        // 重新标记节点为可用
        markNodeAsAvailable(nodeId);

        // 同步数据状态
        syncNodeData(nodeId);

        // 重新平衡负载
        rebalanceLoad();
    }

    private void syncNodeData(String nodeId) {
        // 同步座位状态缓存
        syncSeatStatusCache(nodeId);

        // 同步配置信息
        syncConfigurationData(nodeId);

        // 同步预约数据
        syncReservationData(nodeId);
    }

    @Async
    private void syncSeatStatusCache(String nodeId) {
        // 从中央控制服务获取最新的座位状态
        Map<String, SeatStatus> latestSeatStatus = centralControlClient
            .getAllSeatStatus();

        // 推送到恢复的节点
        nodeClient.updateSeatStatusCache(nodeId, latestSeatStatus);
    }
}
```

## 11. 部署和运维

### 11.1 Kubernetes部署配置

```yaml
# k8s-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: reservation-service
spec:
  replicas: 3
  selector:
    matchLabels:
      app: reservation-service
  template:
    metadata:
      labels:
        app: reservation-service
    spec:
      containers:
      - name: reservation-service
        image: seatmaster/reservation-service:latest
        ports:
        - containerPort: 8080
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "k8s"
        - name: SPRING_DATASOURCE_URL
          valueFrom:
            secretKeyRef:
              name: db-secret
              key: url
        - name: SPRING_REDIS_HOST
          value: "redis-service"
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /actuator/health/readiness
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10

---
apiVersion: v1
kind: Service
metadata:
  name: reservation-service
spec:
  selector:
    app: reservation-service
  ports:
  - port: 8080
    targetPort: 8080
  type: ClusterIP

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: reservation-service-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: reservation-service
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
```

### 11.2 监控和日志

```yaml
# prometheus-config.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'reservation-service'
    static_configs:
      - targets: ['reservation-service:8080']
    metrics_path: '/actuator/prometheus'

  - job_name: 'central-control'
    static_configs:
      - targets: ['central-control:8080']
    metrics_path: '/actuator/prometheus'

  - job_name: 'gateway'
    static_configs:
      - targets: ['gateway:8080']
    metrics_path: '/actuator/prometheus'
```

```yaml
# logback-spring.xml
<configuration>
    <springProfile name="!local">
        <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
            <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
                <providers>
                    <timestamp/>
                    <logLevel/>
                    <loggerName/>
                    <mdc/>
                    <message/>
                    <stackTrace/>
                </providers>
            </encoder>
        </appender>
    </springProfile>

    <logger name="com.seatmaster" level="INFO"/>
    <logger name="org.springframework.cloud.gateway" level="DEBUG"/>

    <root level="INFO">
        <appender-ref ref="STDOUT"/>
    </root>
</configuration>
```

## 12. 总结

本分布式座位预约系统设计方案提供了完整的技术实现路径，主要优势：

1. **架构优势**：基于Spring Cloud生态，成熟稳定
2. **性能优势**：多级缓存、负载均衡、异步处理
3. **可靠性**：故障检测、自动恢复、数据一致性保证
4. **可扩展性**：水平扩展、动态负载均衡
5. **运维友好**：完整的监控、日志、部署方案

该方案可以支持高并发座位预约场景，有效解决传统单体应用的性能瓶颈问题。
