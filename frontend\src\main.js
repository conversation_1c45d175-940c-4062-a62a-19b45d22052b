import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

// 导入 ResizeObserver 错误修复
import './utils/resizeObserverFix.js'

// 开发环境下导入错误测试工具
if (process.env.NODE_ENV === 'development') {
  import('./utils/errorTest.js')
}

const app = createApp(App)

// 注册所有图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// Vue 应用级错误处理
app.config.errorHandler = (err, vm, info) => {
  if (
    err.message &&
    (err.message.includes('ResizeObserver loop completed') ||
     err.message.includes('ResizeObserver loop limit exceeded'))
  ) {
    // 忽略 ResizeObserver 相关错误
    return
  }
  console.error('Vue Error:', err, info)
}

app.use(createPinia())
app.use(router)
app.use(ElementPlus)

app.mount('#app')