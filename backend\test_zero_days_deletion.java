// 测试剩余天数为0时删除预约功能的Java测试代码
// 这个文件展示了如何测试新的删除逻辑

package com.seatmaster.test;

import com.seatmaster.service.impl.ReservationServiceImpl;
import com.seatmaster.mapper.UserMapper;
import com.seatmaster.mapper.ReservationMapper;
import com.seatmaster.entity.User;
import com.seatmaster.entity.Reservation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.AfterEach;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import java.time.LocalTime;
import java.time.LocalDateTime;
import java.util.List;

@SpringBootTest
@SpringJUnitConfig
public class ZeroDaysDeletionTest {

    @Autowired
    private ReservationServiceImpl reservationService;
    
    @Autowired
    private UserMapper userMapper;
    
    @Autowired
    private ReservationMapper reservationMapper;
    
    private Long testUserId;
    private Long testReservationId;

    @BeforeEach
    public void setUp() {
        // 创建测试用户
        User testUser = new User();
        testUser.setUsername("test_zero_days_user");
        testUser.setPassword("password");
        testUser.setName("测试零天用户");
        testUser.setRole(User.Role.USER);
        testUser.setRemainingDays(0); // 设置剩余天数为0
        testUser.setCreatedTime(LocalDateTime.now());
        
        userMapper.insert(testUser);
        testUserId = testUser.getId();
        
        // 为测试用户创建预约
        Reservation testReservation = new Reservation();
        testReservation.setUserId(testUserId);
        testReservation.setRoomId(1L);
        testReservation.setSeatId("TEST001");
        testReservation.setStartTime(LocalTime.of(9, 0));
        testReservation.setEndTime(LocalTime.of(17, 0));
        testReservation.setStatus("ACTIVE");
        testReservation.setCreatedTime(LocalDateTime.now());
        testReservation.setReservationOpenTime("09:00");
        testReservation.setReservationType("SAME_DAY");
        
        reservationMapper.insert(testReservation);
        testReservationId = testReservation.getId();
        
        System.out.println("测试数据创建完成:");
        System.out.println("测试用户ID: " + testUserId + ", 剩余天数: 0");
        System.out.println("测试预约ID: " + testReservationId + ", 状态: ACTIVE");
    }

    @Test
    public void testCheckAndDeleteUserReservationsIfZeroDays() {
        System.out.println("=== 测试立即检查并删除零天用户预约 ===");
        
        // 验证预约存在
        Reservation beforeReservation = reservationMapper.selectById(testReservationId);
        assert beforeReservation != null : "预约应该存在";
        assert "ACTIVE".equals(beforeReservation.getStatus()) : "预约状态应该是ACTIVE";
        System.out.println("删除前: 预约存在，状态为 " + beforeReservation.getStatus());
        
        // 执行检查并删除方法
        reservationService.checkAndDeleteUserReservationsIfZeroDays(testUserId);
        
        // 验证预约已被删除
        Reservation afterReservation = reservationMapper.selectById(testReservationId);
        assert afterReservation == null : "预约应该已被删除";
        System.out.println("删除后: 预约已被删除");
        
        // 验证用户仍然存在
        User user = userMapper.selectById(testUserId);
        assert user != null : "用户应该仍然存在";
        assert user.getRemainingDays() == 0 : "用户剩余天数应该仍为0";
        System.out.println("用户仍存在，剩余天数: " + user.getRemainingDays());
    }

    @Test
    public void testDeleteReservationsForUsersWithZeroDays() {
        System.out.println("=== 测试批量删除零天用户预约 ===");
        
        // 验证预约存在
        QueryWrapper<Reservation> wrapper = new QueryWrapper<>();
        wrapper.eq("user_id", testUserId).eq("status", "ACTIVE");
        List<Reservation> beforeReservations = reservationMapper.selectList(wrapper);
        assert !beforeReservations.isEmpty() : "应该有活跃预约";
        System.out.println("删除前: 找到 " + beforeReservations.size() + " 个活跃预约");
        
        // 执行批量删除方法
        reservationService.deleteReservationsForUsersWithZeroDays();
        
        // 验证预约已被删除
        List<Reservation> afterReservations = reservationMapper.selectList(wrapper);
        assert afterReservations.isEmpty() : "所有活跃预约应该已被删除";
        System.out.println("删除后: 活跃预约数量为 " + afterReservations.size());
    }

    @Test
    public void testUserWithPositiveDaysNotAffected() {
        System.out.println("=== 测试剩余天数大于0的用户不受影响 ===");
        
        // 修改测试用户的剩余天数为1
        User user = userMapper.selectById(testUserId);
        user.setRemainingDays(1);
        userMapper.updateById(user);
        
        // 重新创建预约（因为之前的测试可能已删除）
        Reservation newReservation = new Reservation();
        newReservation.setUserId(testUserId);
        newReservation.setRoomId(1L);
        newReservation.setSeatId("TEST002");
        newReservation.setStartTime(LocalTime.of(10, 0));
        newReservation.setEndTime(LocalTime.of(18, 0));
        newReservation.setStatus("ACTIVE");
        newReservation.setCreatedTime(LocalDateTime.now());
        newReservation.setReservationOpenTime("10:00");
        newReservation.setReservationType("SAME_DAY");
        
        reservationMapper.insert(newReservation);
        Long newReservationId = newReservation.getId();
        
        System.out.println("用户剩余天数修改为1，创建新预约ID: " + newReservationId);
        
        // 执行删除方法
        reservationService.checkAndDeleteUserReservationsIfZeroDays(testUserId);
        
        // 验证预约仍然存在
        Reservation afterReservation = reservationMapper.selectById(newReservationId);
        assert afterReservation != null : "剩余天数大于0的用户预约不应该被删除";
        System.out.println("验证通过: 剩余天数大于0的用户预约未被删除");
        
        // 清理新创建的预约
        reservationMapper.deleteById(newReservationId);
    }

    @AfterEach
    public void tearDown() {
        // 清理测试数据
        if (testReservationId != null) {
            reservationMapper.deleteById(testReservationId);
        }
        if (testUserId != null) {
            userMapper.deleteById(testUserId);
        }
        System.out.println("测试数据清理完成");
    }
}
