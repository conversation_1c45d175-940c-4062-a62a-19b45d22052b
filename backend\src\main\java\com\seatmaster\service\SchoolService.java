package com.seatmaster.service;

import com.seatmaster.entity.School;

import java.util.List;

public interface SchoolService {

    /**
     * 获取所有学校列表
     */
    List<School> getAllSchools();

    /**
     * 根据ID获取学校
     */
    School getSchoolById(Long id);

    /**
     * 创建学校
     */
    School createSchool(School school);

    /**
     * 更新学校
     */
    School updateSchool(School school);

    /**
     * 删除学校
     */
    boolean deleteSchool(Long id);
}