package com.seatmaster.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

/**
 * HTTP客户端配置
 * 为学习通API调用提供RestTemplate Bean
 */
@Configuration
public class HttpClientConfig {
    
    /**
     * 配置RestTemplate Bean
     * 用于学习通API调用
     * 
     * @return RestTemplate实例
     */
    @Bean
    public RestTemplate restTemplate() {
        // 创建HTTP请求工厂
        HttpComponentsClientHttpRequestFactory factory = new HttpComponentsClientHttpRequestFactory();
        
        // 设置连接超时时间 (10秒)
        factory.setConnectTimeout(10000);
        
        // 设置读取超时时间 (30秒)
        factory.setReadTimeout(30000);
        
        // 设置连接请求超时时间 (5秒)
        factory.setConnectionRequestTimeout(5000);
        
        // 创建RestTemplate实例
        RestTemplate restTemplate = new RestTemplate(factory);
        
        return restTemplate;
    }
}
