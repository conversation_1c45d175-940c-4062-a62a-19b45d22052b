package com.seatmaster.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.seatmaster.entity.AutoExecutionLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 自动预约执行日志Mapper接口
 */
@Mapper
public interface AutoExecutionLogMapper extends BaseMapper<AutoExecutionLog> {
    
    /**
     * 根据预约ID查询执行日志
     * 
     * @param reservationId 预约ID
     * @return 执行日志列表
     */
    @Select("SELECT * FROM auto_execution_logs WHERE reservation_id = #{reservationId} ORDER BY execution_time DESC")
    List<AutoExecutionLog> selectByReservationId(@Param("reservationId") Long reservationId);
    
    /**
     * 根据用户ID查询执行日志
     * 
     * @param userId 用户ID
     * @return 执行日志列表
     */
    @Select("SELECT * FROM auto_execution_logs WHERE user_id = #{userId} ORDER BY execution_time DESC LIMIT #{limit}")
    List<AutoExecutionLog> selectByUserId(@Param("userId") Long userId, @Param("limit") Integer limit);
    
    /**
     * 查询指定时间范围内的执行日志
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 执行日志列表
     */
    @Select("SELECT * FROM auto_execution_logs WHERE execution_time BETWEEN #{startTime} AND #{endTime} ORDER BY execution_time DESC")
    List<AutoExecutionLog> selectByTimeRange(@Param("startTime") LocalDateTime startTime, 
                                           @Param("endTime") LocalDateTime endTime);
    
    /**
     * 统计用户的执行成功率
     * 
     * @param userId 用户ID
     * @param days 统计天数
     * @return 成功率(0-1之间的小数)
     */
    @Select("SELECT " +
            "CASE WHEN COUNT(*) = 0 THEN 0 " +
            "ELSE CAST(SUM(CASE WHEN execution_status = 'SUCCESS' THEN 1 ELSE 0 END) AS DECIMAL(10,2)) / COUNT(*) END " +
            "FROM auto_execution_logs " +
            "WHERE user_id = #{userId} AND execution_time >= DATE_SUB(NOW(), INTERVAL #{days} DAY)")
    Double getSuccessRateByUserId(@Param("userId") Long userId, @Param("days") Integer days);
    
    /**
     * 统计系统整体执行情况
     * 
     * @param days 统计天数
     * @return 统计结果Map
     */
    @Select("SELECT " +
            "COUNT(*) as total_count, " +
            "SUM(CASE WHEN execution_status = 'SUCCESS' THEN 1 ELSE 0 END) as success_count, " +
            "SUM(CASE WHEN execution_status = 'FAILED' THEN 1 ELSE 0 END) as failed_count, " +
            "SUM(CASE WHEN execution_status = 'TIMEOUT' THEN 1 ELSE 0 END) as timeout_count, " +
            "AVG(execution_duration) as avg_duration " +
            "FROM auto_execution_logs " +
            "WHERE execution_time >= DATE_SUB(NOW(), INTERVAL #{days} DAY)")
    List<java.util.Map<String, Object>> getSystemStatistics(@Param("days") Integer days);
}
