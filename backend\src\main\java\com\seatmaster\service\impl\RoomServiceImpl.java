package com.seatmaster.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.seatmaster.entity.Room;
import com.seatmaster.mapper.RoomMapper;
import com.seatmaster.service.RoomService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class RoomServiceImpl implements RoomService {

    private final RoomMapper roomMapper;

    @Override
    public List<Room> getAllRooms() {
        log.info("RoomServiceImpl: 开始获取所有房间");
        List<Room> rooms = roomMapper.findAllWithAvailableSeats();
        log.info("RoomServiceImpl: 获取到{}个房间", rooms.size());
        return rooms;
    }

    @Override
    public List<Room> getRoomsBySchoolId(Long schoolId) {
        log.info("RoomServiceImpl: 开始获取学校{}的房间", schoolId);
        QueryWrapper<Room> wrapper = new QueryWrapper<>();
        wrapper.eq("school_id", schoolId);
        List<Room> rooms = roomMapper.selectList(wrapper);
        log.info("RoomServiceImpl: 获取到{}个房间", rooms.size());
        return rooms;
    }
    
    @Override
    public Room getRoomById(Long id) {
        return roomMapper.selectById(id);
    }
    
    @Override
    public Room createRoom(Room room) {
        room.setCreatedTime(java.time.LocalDateTime.now());
        if (room.getVersion() == null) {
            room.setVersion(0);
        }
        roomMapper.insert(room);
        return room;
    }
    
    @Override
    public Room updateRoom(Room room) {
        roomMapper.updateById(room);
        return room;
    }
    
    @Override
    public boolean deleteRoom(Long id) {
        return roomMapper.deleteById(id) > 0;
    }
}
